import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  Modal,
  Pressable,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useCycleData } from "../hooks/useCycleData";
import { SettingsStorage, UserSettings } from "../utils/storage";
import { showToast } from "../components/ToastWrapper";
import { THEME_COLORS } from "../types/constants";
import { PDFExportService } from "../utils/pdfExport";
import { NotificationService } from "../utils/notificationService";

export const SettingsScreen: React.FC = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { clearAllData, entries, stats, exportData } = useCycleData();
  const [settings, setSettings] = useState<UserSettings | null>(null);
  const [clearConfirmation, setClearConfirmation] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [isUpdatingReminder, setIsUpdatingReminder] = useState(false);
  const hourScrollRef = useRef<ScrollView>(null);
  const minuteScrollRef = useRef<ScrollView>(null);

  useEffect(() => {
    loadSettings();
    // Initialize notification service
    NotificationService.initialize();
  }, []);

  const loadSettings = async () => {
    try {
      const userSettings = await SettingsStorage.loadSettings();
      setSettings(userSettings);
    } catch (error) {
      console.error("Error loading settings:", error);
    }
  };

  const formatTime = (timeString: string): string => {
    try {
      const [hours, minutes] = timeString.split(":").map(Number);
      const period = hours >= 12 ? "PM" : "AM";
      const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
      return `${displayHours}:${minutes.toString().padStart(2, "0")} ${period}`;
    } catch (error) {
      return timeString;
    }
  };

  const updateSetting = async (key: keyof UserSettings, value: any) => {
    if (!settings) return;

    try {
      const newSettings = { ...settings, [key]: value };
      await SettingsStorage.updateSettings({ [key]: value });
      setSettings(newSettings);

      // Handle reminder-specific logic
      if (key === "reminderEnabled") {
        setIsUpdatingReminder(true);
        const success = await NotificationService.updateReminderSettings(
          newSettings
        );
        setIsUpdatingReminder(false);

        if (!success && value) {
          // If enabling reminders failed, revert the setting
          const revertedSettings = { ...settings, reminderEnabled: false };
          await SettingsStorage.updateSettings({ reminderEnabled: false });
          setSettings(revertedSettings);
        }
        return; // Don't show additional toast as NotificationService handles it
      }

      // Provide specific toast messages for other settings
      switch (key) {
        case "reminderTime":
          // Handle reminder time change
          if (settings.reminderEnabled) {
            setIsUpdatingReminder(true);
            await NotificationService.updateReminderSettings(newSettings);
            setIsUpdatingReminder(false);
          }
          showToast.settings(
            "Reminder Time Updated",
            `Daily reminders set for ${formatTime(value)}`
          );
          break;
        case "firstDayOfWeek":
          // Week start change is handled separately in handleWeekStartChange
          break;
        default:
          showToast.settings(
            "Settings Updated",
            "Your preferences have been saved"
          );
      }
    } catch (error) {
      console.error("Error updating settings:", error);
      showToast.error("Update Failed", "Could not save settings. Try again.");
      setIsUpdatingReminder(false);
    }
  };

  const handleExportData = async () => {
    // Show initial toast
    showToast.info("Creating PDF", "Generating cycle data report...");

    try {
      const result = await PDFExportService.exportCycleDataToPDF(
        entries,
        stats
      );

      if (result.success) {
        // Extract just the filename from the full path for user display
        const fileName =
          result.filePath.split("/").pop() ||
          result.filePath ||
          "LocalOne_Period_Cycle_Export.pdf";

        showToast.success("PDF Ready", `Report "${fileName}" ready to save`);
      } else {
        showToast.error("PDF Failed", result.error);
      }
    } catch (error) {
      console.error("Error exporting PDF:", error);

      // Show specific error message from the PDF service
      const errorMessage =
        error instanceof Error ? error.message : "Could not create PDF export";

      showToast.error("PDF Failed", errorMessage);
    }
  };

  const handleClearData = () => {
    if (!clearConfirmation) {
      // First tap - show warning toast and set confirmation state
      setClearConfirmation(true);
      showToast.warning(
        "Clear All Data",
        "Tap again within 10 seconds to permanently delete all your cycle data"
      );

      // Reset confirmation after 10 seconds
      setTimeout(() => {
        setClearConfirmation(false);
      }, 10000);
    } else {
      // Second tap - show final confirmation dialog
      setClearConfirmation(false); // Reset state

      Alert.alert(
        "⚠️ Final Confirmation",
        "This will permanently delete ALL your cycle data. This action cannot be undone.\n\nAre you absolutely sure?",
        [
          {
            text: "Cancel",
            style: "cancel",
            onPress: () => {
              showToast.info("Action Cancelled", "Your data is safe");
            },
          },
          {
            text: "Delete Everything",
            style: "destructive",
            onPress: async () => {
              try {
                await clearAllData();
                showToast.success(
                  "All Data Cleared",
                  "Cycle history deleted. Start fresh!"
                );
              } catch (error) {
                console.error("Error clearing data:", error);
                showToast.error(
                  "Clear Failed",
                  "Could not delete data. Try again."
                );
              }
            },
          },
        ]
      );
    }
  };

  const handleWeekStartChange = () => {
    const currentDay = settings?.firstDayOfWeek === 0 ? "Sunday" : "Monday";
    const newDay = settings?.firstDayOfWeek === 0 ? "Monday" : "Sunday";
    const newValue = settings?.firstDayOfWeek === 0 ? 1 : 0;

    updateSetting("firstDayOfWeek", newValue);
    showToast.settings(
      "Week Start Updated",
      `Calendar now starts on ${newDay} instead of ${currentDay}`
    );
  };

  const handleTimePickerOpen = () => {
    setShowTimePicker(true);

    // Auto-scroll to current time after modal opens
    setTimeout(() => {
      if (settings) {
        const currentHour = parseInt(settings.reminderTime.split(":")[0]);
        const currentMinute = parseInt(settings.reminderTime.split(":")[1]);

        // Scroll to current hour (each option is ~50px high)
        hourScrollRef.current?.scrollTo({
          y: currentHour * 50,
          animated: true,
        });

        // Scroll to current minute (5-minute intervals: 0, 5, 10, 15, etc.)
        const minuteIndex = Math.floor(currentMinute / 5);
        minuteScrollRef.current?.scrollTo({
          y: minuteIndex * 50,
          animated: true,
        });
      }
    }, 300); // Small delay to ensure modal is fully rendered
  };

  const handleTimeChange = (hours: number, minutes: number) => {
    const timeString = `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}`;
    updateSetting("reminderTime", timeString);
    setShowTimePicker(false);
  };

  if (!settings) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Settings</Text>
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading settings...</Text>
        </View>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={{
        paddingTop: insets.top,
        paddingBottom:
          Platform.OS === "ios" ? insets.bottom + 100 : insets.bottom + 20, // Extra padding for iOS tab bar
      }}
    >
      <View style={styles.header}>
        <Text style={styles.title}>Settings</Text>
        <Text style={styles.subtitle}>Customize your experience</Text>
      </View>

      {/* Data Management */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Data Management</Text>

        <TouchableOpacity style={styles.settingItem} onPress={handleExportData}>
          <View style={styles.settingLeft}>
            <Text style={styles.settingIcon}>📄</Text>
            <View>
              <Text style={styles.settingTitle}>Export as PDF</Text>
              <Text style={styles.settingDescription}>
                Create and share a PDF report
              </Text>
            </View>
          </View>
          <Text style={styles.settingArrow}>›</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.settingItem} onPress={handleClearData}>
          <View style={styles.settingLeft}>
            <Text style={styles.settingIcon}>
              {clearConfirmation ? "⚠️" : "🗑️"}
            </Text>
            <View>
              <Text style={[styles.settingTitle, styles.dangerText]}>
                {clearConfirmation ? "Tap Again to Confirm" : "Clear All Data"}
              </Text>
              <Text style={styles.settingDescription}>
                {clearConfirmation
                  ? "This will permanently delete all your cycle data"
                  : "Permanently delete all cycle data"}
              </Text>
            </View>
          </View>
          <Text style={styles.settingArrow}>
            {clearConfirmation ? "⚠️" : "›"}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Cycle Preferences */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Cycle Preferences</Text>

        <TouchableOpacity
          style={styles.settingItem}
          onPress={() => handleWeekStartChange()}
        >
          <View style={styles.settingLeft}>
            <Text style={styles.settingIcon}>📆</Text>
            <View>
              <Text style={styles.settingTitle}>Week Starts On</Text>
              <Text style={styles.settingDescription}>
                First day of the week in calendar
              </Text>
            </View>
          </View>
          <Text style={styles.settingValue}>
            {settings.firstDayOfWeek === 0 ? "Sunday" : "Monday"}
          </Text>
          <Text style={styles.settingArrow}>›</Text>
        </TouchableOpacity>
      </View>

      {/* App Preferences */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notifications</Text>

        <View style={styles.settingItem}>
          <View style={styles.settingLeft}>
            <Text style={styles.settingIcon}>🔔</Text>
            <View>
              <Text style={styles.settingTitle}>Daily Reminders</Text>
              <Text style={styles.settingDescription}>
                {settings.reminderEnabled
                  ? `Enabled at ${formatTime(settings.reminderTime)}`
                  : "Get notified to log your cycle data"}
              </Text>
            </View>
          </View>
          <Switch
            value={settings.reminderEnabled}
            onValueChange={(value) => updateSetting("reminderEnabled", value)}
            disabled={isUpdatingReminder}
            trackColor={{
              false: THEME_COLORS.border,
              true: THEME_COLORS.primary + "40",
            }}
            thumbColor={
              settings.reminderEnabled
                ? THEME_COLORS.primary
                : THEME_COLORS.textSecondary
            }
          />
        </View>

        {settings.reminderEnabled && (
          <>
            <TouchableOpacity
              style={styles.settingItem}
              onPress={handleTimePickerOpen}
            >
              <View style={styles.settingLeft}>
                <Text style={styles.settingIcon}>⏰</Text>
                <View>
                  <Text style={styles.settingTitle}>Reminder Time</Text>
                  <Text style={styles.settingDescription}>
                    When to send daily notifications
                  </Text>
                </View>
              </View>
              <Text style={styles.settingValue}>
                {formatTime(settings.reminderTime)}
              </Text>
              <Text style={styles.settingArrow}>›</Text>
            </TouchableOpacity>
          </>
        )}
      </View>

      {/* About */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>About</Text>

        <TouchableOpacity
          style={styles.settingItem}
          onPress={() => router.push("/about")}
        >
          <View style={styles.settingLeft}>
            <Text style={styles.settingIcon}>ℹ️</Text>
            <View>
              <Text style={styles.settingTitle}>
                About LocalOne Period & Cycle
              </Text>
              <Text style={styles.settingDescription}>
                App info, privacy policy, and other apps
              </Text>
            </View>
          </View>
          <Text style={styles.settingArrow}>›</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.bottomSpacing} />

      {/* Time Picker Modal */}
      <Modal
        visible={showTimePicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowTimePicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.timePickerContainer}>
            <Text style={styles.timePickerTitle}>Set Reminder Time</Text>

            <View style={styles.timePickerContent}>
              {/* Simple time selection - hours */}
              <View style={styles.timeColumn}>
                <Text style={styles.timeLabel}>Hour</Text>
                <ScrollView
                  ref={hourScrollRef}
                  style={styles.timeScroll}
                  showsVerticalScrollIndicator={false}
                >
                  {Array.from({ length: 24 }, (_, i) => (
                    <Pressable
                      key={i}
                      style={[
                        styles.timeOption,
                        parseInt(
                          settings?.reminderTime.split(":")[0] || "9"
                        ) === i && styles.timeOptionSelected,
                      ]}
                      onPress={() => {
                        const currentMinutes = parseInt(
                          settings?.reminderTime.split(":")[1] || "0"
                        );
                        handleTimeChange(i, currentMinutes);
                      }}
                    >
                      <Text
                        style={[
                          styles.timeOptionText,
                          parseInt(
                            settings?.reminderTime.split(":")[0] || "9"
                          ) === i && styles.timeOptionTextSelected,
                        ]}
                      >
                        {i.toString().padStart(2, "0")}
                      </Text>
                    </Pressable>
                  ))}
                </ScrollView>
              </View>

              {/* Minutes */}
              <View style={styles.timeColumn}>
                <Text style={styles.timeLabel}>Minute</Text>
                <ScrollView
                  ref={minuteScrollRef}
                  style={styles.timeScroll}
                  showsVerticalScrollIndicator={false}
                >
                  {Array.from({ length: 12 }, (_, i) => i * 5).map((minute) => (
                    <Pressable
                      key={minute}
                      style={[
                        styles.timeOption,
                        parseInt(
                          settings?.reminderTime.split(":")[1] || "0"
                        ) === minute && styles.timeOptionSelected,
                      ]}
                      onPress={() => {
                        const currentHours = parseInt(
                          settings?.reminderTime.split(":")[0] || "9"
                        );
                        handleTimeChange(currentHours, minute);
                      }}
                    >
                      <Text
                        style={[
                          styles.timeOptionText,
                          parseInt(
                            settings?.reminderTime.split(":")[1] || "0"
                          ) === minute && styles.timeOptionTextSelected,
                        ]}
                      >
                        {minute.toString().padStart(2, "0")}
                      </Text>
                    </Pressable>
                  ))}
                </ScrollView>
              </View>
            </View>

            <View style={styles.timePickerButtons}>
              <Pressable
                style={[styles.timePickerButton, styles.cancelButton]}
                onPress={() => setShowTimePicker(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </Pressable>
            </View>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME_COLORS.background,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: THEME_COLORS.text,
  },
  subtitle: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    marginTop: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: THEME_COLORS.text,
    marginBottom: 12,
    marginHorizontal: 20,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 20,
    backgroundColor: THEME_COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: THEME_COLORS.border,
  },
  settingLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  settingIcon: {
    fontSize: 20,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: THEME_COLORS.text,
  },
  settingDescription: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    marginTop: 2,
  },
  settingValue: {
    fontSize: 16,
    color: THEME_COLORS.primary,
    fontWeight: "500",
  },
  settingArrow: {
    fontSize: 20,
    color: THEME_COLORS.textSecondary,
  },
  dangerText: {
    color: THEME_COLORS.error,
  },
  aboutCard: {
    backgroundColor: THEME_COLORS.surface,
    marginHorizontal: 16,
    borderRadius: 12,
    padding: 20,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  aboutTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: THEME_COLORS.primary,
    textAlign: "center",
  },
  aboutVersion: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    textAlign: "center",
    marginTop: 4,
    marginBottom: 16,
  },
  aboutDescription: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    lineHeight: 20,
    textAlign: "center",
    marginBottom: 20,
  },
  dataStats: {
    borderTopWidth: 1,
    borderTopColor: THEME_COLORS.border,
    paddingTop: 16,
  },
  dataStatsTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: THEME_COLORS.text,
    marginBottom: 8,
  },
  dataStatsText: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    marginBottom: 4,
  },
  bottomSpacing: {
    height: 40,
  },
  // Time picker modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  timePickerContainer: {
    backgroundColor: THEME_COLORS.surface,
    borderRadius: 16,
    padding: 24,
    margin: 20,
    width: "80%",
    maxWidth: 300,
  },
  timePickerTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: THEME_COLORS.text,
    textAlign: "center",
    marginBottom: 20,
  },
  timePickerContent: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 20,
  },
  timeColumn: {
    flex: 1,
    alignItems: "center",
  },
  timeLabel: {
    fontSize: 16,
    fontWeight: "500",
    color: THEME_COLORS.text,
    marginBottom: 10,
  },
  timeScroll: {
    height: 120,
    width: 60,
  },
  timeOption: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginVertical: 2,
    alignItems: "center",
  },
  timeOptionSelected: {
    backgroundColor: THEME_COLORS.primary,
  },
  timeOptionText: {
    fontSize: 16,
    color: THEME_COLORS.text,
  },
  timeOptionTextSelected: {
    color: "white",
    fontWeight: "600",
  },
  timePickerButtons: {
    flexDirection: "row",
    justifyContent: "center",
  },
  timePickerButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 80,
    alignItems: "center",
  },
  cancelButton: {
    backgroundColor: THEME_COLORS.border,
  },
  cancelButtonText: {
    fontSize: 16,
    color: THEME_COLORS.text,
    fontWeight: "500",
  },
});
