import * as Notifications from "expo-notifications";
import { Platform } from "react-native";
import { SettingsStorage, UserSettings } from "./storage";
import { showToast } from "../components/ToastWrapper";

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowBanner: true,
    shouldShowList: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export interface NotificationPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: Notifications.PermissionStatus;
}

export class NotificationService {
  private static readonly REMINDER_IDENTIFIER = "cycle-reminder";
  private static readonly CHANNEL_ID = "cycle-reminders";

  /**
   * Initialize the notification service
   */
  static async initialize(): Promise<void> {
    try {
      // Create notification channel for Android
      if (Platform.OS === "android") {
        await Notifications.setNotificationChannelAsync(this.CHANNEL_ID, {
          name: "Cycle Reminders",
          description: "Daily reminders to log your cycle data",
          importance: Notifications.AndroidImportance.DEFAULT,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: "#B19CD9",
          sound: "default",
        });
      }
    } catch (error) {
      console.error("Error initializing notification service:", error);
    }
  }

  /**
   * Request notification permissions from the user
   */
  static async requestPermissions(): Promise<NotificationPermissionStatus> {
    try {
      const { status: existingStatus } =
        await Notifications.getPermissionsAsync();

      let finalStatus = existingStatus;

      if (existingStatus !== "granted") {
        const { status } = await Notifications.requestPermissionsAsync({
          ios: {
            allowAlert: true,
            allowBadge: false,
            allowSound: true,
            allowDisplayInCarPlay: false,
            allowCriticalAlerts: false,
            provideAppNotificationSettings: false,
            allowProvisional: false,
          },
        });
        finalStatus = status;
      }

      return {
        granted: finalStatus === "granted",
        canAskAgain: finalStatus !== "denied",
        status: finalStatus,
      };
    } catch (error) {
      console.error("Error requesting notification permissions:", error);
      return {
        granted: false,
        canAskAgain: false,
        status: Notifications.PermissionStatus.UNDETERMINED,
      };
    }
  }

  /**
   * Check current notification permission status
   */
  static async getPermissionStatus(): Promise<NotificationPermissionStatus> {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      return {
        granted: status === "granted",
        canAskAgain: status !== "denied",
        status,
      };
    } catch (error) {
      console.error("Error getting permission status:", error);
      return {
        granted: false,
        canAskAgain: false,
        status: Notifications.PermissionStatus.UNDETERMINED,
      };
    }
  }

  /**
   * Schedule daily reminder notifications
   */
  static async scheduleReminder(reminderTime: string): Promise<boolean> {
    try {
      // First check permissions
      const permissionStatus = await this.getPermissionStatus();

      if (!permissionStatus.granted) {
        const requestResult = await this.requestPermissions();

        if (!requestResult.granted) {
          showToast.error(
            "Permission Required",
            "Enable notifications in settings for reminders"
          );
          return false;
        }
      }

      // Cancel existing reminders
      await this.cancelReminder();

      // Parse reminder time (HH:MM format)
      const [hours, minutes] = reminderTime.split(":").map(Number);

      if (
        isNaN(hours) ||
        isNaN(minutes) ||
        hours < 0 ||
        hours > 23 ||
        minutes < 0 ||
        minutes > 59
      ) {
        throw new Error("Invalid reminder time format");
      }

      // Schedule the notification using DailyTriggerInput for proper daily repeats
      await Notifications.scheduleNotificationAsync({
        identifier: this.REMINDER_IDENTIFIER,
        content: {
          title: "🌙 LunaCycle Reminder",
          body: "Time to log your cycle data! Track your symptoms, mood, and period status.",
          sound: "default",
          data: {
            type: "cycle-reminder",
            action: "open-log-entry",
          },
        },
        trigger: {
          type: Notifications.SchedulableTriggerInputTypes.DAILY,
          hour: hours,
          minute: minutes,
          repeats: true,
        } as Notifications.DailyTriggerInput,
      });

      return true;
    } catch (error) {
      console.error("Error scheduling reminder:", error);
      showToast.error(
        "Reminder Failed",
        "Could not schedule daily reminder. Try again."
      );
      return false;
    }
  }

  /**
   * Cancel the daily reminder
   */
  static async cancelReminder(): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(
        this.REMINDER_IDENTIFIER
      );
    } catch (error) {
      console.error("Error cancelling reminder:", error);
    }
  }

  /**
   * Get all scheduled notifications (for debugging)
   */
  static async getScheduledNotifications(): Promise<
    Notifications.NotificationRequest[]
  > {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error("Error getting scheduled notifications:", error);
      return [];
    }
  }

  /**
   * Handle enabling/disabling reminders based on user settings
   */
  static async updateReminderSettings(
    settings: UserSettings
  ): Promise<boolean> {
    try {
      if (settings.reminderEnabled) {
        const success = await this.scheduleReminder(settings.reminderTime);
        if (success) {
          showToast.success(
            "Reminders Enabled",
            `Daily reminders set for ${this.formatTime(settings.reminderTime)}`
          );
        }
        return success;
      } else {
        await this.cancelReminder();
        showToast.info(
          "Reminders Disabled",
          "Daily reminder notifications turned off"
        );
        return true;
      }
    } catch (error) {
      console.error("Error updating reminder settings:", error);
      return false;
    }
  }

  /**
   * Format time string for display
   */
  private static formatTime(timeString: string): string {
    try {
      const [hours, minutes] = timeString.split(":").map(Number);
      const period = hours >= 12 ? "PM" : "AM";
      const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
      return `${displayHours}:${minutes.toString().padStart(2, "0")} ${period}`;
    } catch {
      return timeString;
    }
  }

  /**
   * Send an immediate test notification
   */
  static async sendTestNotification(): Promise<boolean> {
    try {
      const permissionStatus = await this.getPermissionStatus();
      if (!permissionStatus.granted) {
        const requestResult = await this.requestPermissions();
        if (!requestResult.granted) {
          return false;
        }
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title: "🌙 Test Notification",
          body: "This is a test reminder from LunaCycle. Your daily reminders will look like this!",
          sound: "default",
        },
        trigger: {
          seconds: 1,
        } as Notifications.TimeIntervalTriggerInput,
      });

      return true;
    } catch (error) {
      console.error("Error sending test notification:", error);
      return false;
    }
  }
}
