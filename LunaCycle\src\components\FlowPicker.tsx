import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { FlowLevel } from '../types/CycleEntry';
import { FLOW_LEVEL_OPTIONS, THEME_COLORS } from '../types/constants';

interface FlowPickerProps {
  selectedFlow?: FlowLevel;
  onFlowSelect: (flow: FlowLevel) => void;
  title?: string;
  style?: any;
}

export const FlowPicker: React.FC<FlowPickerProps> = ({
  selectedFlow,
  onFlowSelect,
  title = 'Flow level',
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      <Text style={styles.title}>{title}</Text>
      <View style={styles.flowContainer}>
        {FLOW_LEVEL_OPTIONS.map((flow) => (
          <TouchableOpacity
            key={flow.value}
            style={[
              styles.flowButton,
              selectedFlow === flow.value && styles.selectedFlowButton,
              { borderColor: flow.color },
              selectedFlow === flow.value && { backgroundColor: flow.color + '20' },
            ]}
            onPress={() => onFlowSelect(flow.value)}
            activeOpacity={0.7}
          >
            <Text style={styles.flowIcon}>{flow.icon}</Text>
            <Text
              style={[
                styles.flowLabel,
                selectedFlow === flow.value && styles.selectedFlowLabel,
              ]}
            >
              {flow.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME_COLORS.text,
    marginBottom: 12,
  },
  flowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  flowButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: THEME_COLORS.border,
    backgroundColor: THEME_COLORS.surface,
  },
  selectedFlowButton: {
    borderWidth: 2,
  },
  flowIcon: {
    fontSize: 20,
    marginBottom: 6,
  },
  flowLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME_COLORS.textSecondary,
    textAlign: 'center',
  },
  selectedFlowLabel: {
    color: THEME_COLORS.text,
    fontWeight: '600',
  },
});
