import AsyncStorage from '@react-native-async-storage/async-storage';
import { CycleEntry } from '../types/CycleEntry';
import { STORAGE_KEYS } from '../types/constants';

// Generic storage utilities
export class Storage {
  static async setItem<T>(key: string, value: T): Promise<void> {
    try {
      const jsonValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, jsonValue);
    } catch (error) {
      console.error(`Error saving data to storage for key ${key}:`, error);
      throw new Error(`Failed to save data: ${error}`);
    }
  }

  static async getItem<T>(key: string): Promise<T | null> {
    try {
      const jsonValue = await AsyncStorage.getItem(key);
      return jsonValue != null ? JSON.parse(jsonValue) : null;
    } catch (error) {
      console.error(`Error reading data from storage for key ${key}:`, error);
      return null;
    }
  }

  static async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error(`Error removing data from storage for key ${key}:`, error);
      throw new Error(`Failed to remove data: ${error}`);
    }
  }

  static async clear(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Error clearing storage:', error);
      throw new Error(`Failed to clear storage: ${error}`);
    }
  }

  static async getAllKeys(): Promise<string[]> {
    try {
      return await AsyncStorage.getAllKeys();
    } catch (error) {
      console.error('Error getting all keys from storage:', error);
      return [];
    }
  }
}

// Cycle-specific storage operations
export class CycleStorage {
  // Save all cycle entries
  static async saveCycleEntries(entries: CycleEntry[]): Promise<void> {
    await Storage.setItem(STORAGE_KEYS.CYCLE_ENTRIES, entries);
  }

  // Load all cycle entries
  static async loadCycleEntries(): Promise<CycleEntry[]> {
    const entries = await Storage.getItem<CycleEntry[]>(STORAGE_KEYS.CYCLE_ENTRIES);
    return entries || [];
  }

  // Add a new cycle entry
  static async addCycleEntry(entry: CycleEntry): Promise<void> {
    const entries = await this.loadCycleEntries();
    
    // Check if entry for this date already exists
    const existingIndex = entries.findIndex(e => e.date === entry.date);
    
    if (existingIndex >= 0) {
      // Update existing entry
      entries[existingIndex] = entry;
    } else {
      // Add new entry
      entries.push(entry);
    }
    
    // Sort entries by date (newest first)
    entries.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    
    await this.saveCycleEntries(entries);
  }

  // Update an existing cycle entry
  static async updateCycleEntry(entryId: string, updates: Partial<CycleEntry>): Promise<void> {
    const entries = await this.loadCycleEntries();
    const entryIndex = entries.findIndex(e => e.id === entryId);
    
    if (entryIndex >= 0) {
      entries[entryIndex] = {
        ...entries[entryIndex],
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      
      await this.saveCycleEntries(entries);
    } else {
      throw new Error(`Entry with ID ${entryId} not found`);
    }
  }

  // Delete a cycle entry
  static async deleteCycleEntry(entryId: string): Promise<void> {
    const entries = await this.loadCycleEntries();
    const filteredEntries = entries.filter(e => e.id !== entryId);
    
    if (filteredEntries.length === entries.length) {
      throw new Error(`Entry with ID ${entryId} not found`);
    }
    
    await this.saveCycleEntries(filteredEntries);
  }

  // Get cycle entry by date
  static async getCycleEntryByDate(date: string): Promise<CycleEntry | null> {
    const entries = await this.loadCycleEntries();
    return entries.find(e => e.date === date) || null;
  }

  // Get cycle entry by ID
  static async getCycleEntryById(entryId: string): Promise<CycleEntry | null> {
    const entries = await this.loadCycleEntries();
    return entries.find(e => e.id === entryId) || null;
  }

  // Get entries within date range
  static async getCycleEntriesInRange(startDate: string, endDate: string): Promise<CycleEntry[]> {
    const entries = await this.loadCycleEntries();
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    return entries.filter(entry => {
      const entryDate = new Date(entry.date);
      return entryDate >= start && entryDate <= end;
    });
  }

  // Get period entries only
  static async getPeriodEntries(): Promise<CycleEntry[]> {
    const entries = await this.loadCycleEntries();
    return entries.filter(entry => entry.isPeriodDay);
  }

  // Clear all cycle data
  static async clearAllCycleData(): Promise<void> {
    await Storage.removeItem(STORAGE_KEYS.CYCLE_ENTRIES);
  }

  // Export cycle data as JSON string
  static async exportCycleData(): Promise<string> {
    const entries = await this.loadCycleEntries();
    return JSON.stringify({
      exportDate: new Date().toISOString(),
      version: '1.0',
      entries,
    }, null, 2);
  }

  // Import cycle data from JSON string
  static async importCycleData(jsonData: string, mergeWithExisting: boolean = false): Promise<void> {
    try {
      const importData = JSON.parse(jsonData);
      
      if (!importData.entries || !Array.isArray(importData.entries)) {
        throw new Error('Invalid import data format');
      }
      
      let entries: CycleEntry[] = importData.entries;
      
      if (mergeWithExisting) {
        const existingEntries = await this.loadCycleEntries();
        const existingDates = new Set(existingEntries.map(e => e.date));
        
        // Only add entries that don't already exist
        const newEntries = entries.filter(e => !existingDates.has(e.date));
        entries = [...existingEntries, ...newEntries];
      }
      
      // Sort entries by date
      entries.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      
      await this.saveCycleEntries(entries);
    } catch (error) {
      throw new Error(`Failed to import cycle data: ${error}`);
    }
  }
}

// User settings storage
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  reminderEnabled: boolean;
  reminderTime: string; // HH:MM format
  cycleLength: number;
  periodDuration: number;
  firstDayOfWeek: 0 | 1; // 0 = Sunday, 1 = Monday
  language: string;
}

export class SettingsStorage {
  static async saveSettings(settings: UserSettings): Promise<void> {
    await Storage.setItem(STORAGE_KEYS.USER_SETTINGS, settings);
  }

  static async loadSettings(): Promise<UserSettings> {
    const settings = await Storage.getItem<UserSettings>(STORAGE_KEYS.USER_SETTINGS);
    
    // Return default settings if none exist
    return settings || {
      theme: 'auto',
      reminderEnabled: false,
      reminderTime: '09:00',
      cycleLength: 28,
      periodDuration: 5,
      firstDayOfWeek: 1,
      language: 'en',
    };
  }

  static async updateSettings(updates: Partial<UserSettings>): Promise<void> {
    const currentSettings = await this.loadSettings();
    const newSettings = { ...currentSettings, ...updates };
    await this.saveSettings(newSettings);
  }
}
