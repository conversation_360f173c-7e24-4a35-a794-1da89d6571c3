// Date formatting and manipulation utilities

export class DateUtils {
  // Format date to YYYY-MM-DD string
  static formatDateToString(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  }

  // Parse YYYY-MM-DD string to Date object in local timezone
  static parseStringToDate(dateString: string): Date {
    const [year, month, day] = dateString.split("-").map(Number);
    return new Date(year, month - 1, day); // month is 0-indexed in Date constructor
  }

  // Get today's date as YYYY-MM-DD string
  static getTodayString(): string {
    return this.formatDateToString(new Date());
  }

  // Add days to a date string
  static addDays(dateString: string, days: number): string {
    const date = this.parseStringToDate(dateString);
    date.setDate(date.getDate() + days);
    return this.formatDateToString(date);
  }

  // Subtract days from a date string
  static subtractDays(dateString: string, days: number): string {
    return this.addDays(dateString, -days);
  }

  // Get difference in days between two date strings
  static getDaysDifference(startDate: string, endDate: string): number {
    const start = this.parseStringToDate(startDate);
    const end = this.parseStringToDate(endDate);
    const diffTime = end.getTime() - start.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  // Check if date string is today
  static isToday(dateString: string): boolean {
    return dateString === this.getTodayString();
  }

  // Check if date string is in the past
  static isPast(dateString: string): boolean {
    const today = this.getTodayString();
    return dateString < today;
  }

  // Check if date string is in the future
  static isFuture(dateString: string): boolean {
    const today = this.getTodayString();
    return dateString > today;
  }

  // Format date for display (e.g., "Jan 15, 2024")
  static formatForDisplay(dateString: string): string {
    const date = this.parseStringToDate(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }

  // Format date for display with day name (e.g., "Monday, Jan 15")
  static formatWithDayName(dateString: string): string {
    const date = this.parseStringToDate(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      month: "short",
      day: "numeric",
    });
  }

  // Get month name from date string
  static getMonthName(dateString: string): string {
    const date = this.parseStringToDate(dateString);
    return date.toLocaleDateString("en-US", { month: "long" });
  }

  // Get year from date string
  static getYear(dateString: string): number {
    const date = this.parseStringToDate(dateString);
    return date.getFullYear();
  }

  // Get first day of month for a given date string
  static getFirstDayOfMonth(dateString: string): string {
    const date = this.parseStringToDate(dateString);
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    return this.formatDateToString(firstDay);
  }

  // Get last day of month for a given date string
  static getLastDayOfMonth(dateString: string): string {
    const date = this.parseStringToDate(dateString);
    const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    return this.formatDateToString(lastDay);
  }

  // Get date range for current month
  static getCurrentMonthRange(): { start: string; end: string } {
    const today = this.getTodayString();
    return {
      start: this.getFirstDayOfMonth(today),
      end: this.getLastDayOfMonth(today),
    };
  }

  // Get date range for a specific month/year
  static getMonthRange(
    year: number,
    month: number
  ): { start: string; end: string } {
    const firstDay = new Date(year, month - 1, 1);
    const lastDay = new Date(year, month, 0);
    return {
      start: this.formatDateToString(firstDay),
      end: this.formatDateToString(lastDay),
    };
  }

  // Generate array of date strings between two dates (inclusive)
  static getDateRange(startDate: string, endDate: string): string[] {
    const dates: string[] = [];
    let currentDate = startDate;

    while (currentDate <= endDate) {
      dates.push(currentDate);
      currentDate = this.addDays(currentDate, 1);
    }

    return dates;
  }

  // Get relative time description (e.g., "2 days ago", "in 3 days")
  static getRelativeTime(dateString: string): string {
    const today = this.getTodayString();
    const daysDiff = this.getDaysDifference(today, dateString);

    if (daysDiff === 0) {
      return "Today";
    } else if (daysDiff === 1) {
      return "Tomorrow";
    } else if (daysDiff === -1) {
      return "Yesterday";
    } else if (daysDiff > 0) {
      return `In ${daysDiff} days`;
    } else {
      return `${Math.abs(daysDiff)} days ago`;
    }
  }

  // Check if two date strings are in the same month
  static isSameMonth(date1: string, date2: string): boolean {
    const d1 = this.parseStringToDate(date1);
    const d2 = this.parseStringToDate(date2);
    return (
      d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth()
    );
  }

  // Get week number of the year
  static getWeekNumber(dateString: string): number {
    const date = this.parseStringToDate(dateString);
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear =
      (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }
}

// Text formatting utilities
export class TextUtils {
  // Capitalize first letter of a string
  static capitalize(text: string): string {
    return text.charAt(0).toUpperCase() + text.slice(1);
  }

  // Convert snake_case to Title Case
  static snakeToTitle(text: string): string {
    return text
      .split("_")
      .map((word) => this.capitalize(word))
      .join(" ");
  }

  // Truncate text with ellipsis
  static truncate(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + "...";
  }

  // Format number with ordinal suffix (1st, 2nd, 3rd, etc.)
  static formatOrdinal(num: number): string {
    const suffixes = ["th", "st", "nd", "rd"];
    const value = num % 100;
    return (
      num + (suffixes[(value - 20) % 10] || suffixes[value] || suffixes[0])
    );
  }

  // Format duration in days
  static formatDuration(days: number): string {
    if (days === 1) return "1 day";
    return `${days} days`;
  }

  // Format percentage
  static formatPercentage(value: number, decimals: number = 1): string {
    return `${value.toFixed(decimals)}%`;
  }

  // Format symptoms array to user-friendly text
  static formatSymptoms(symptoms: string[]): string {
    // Import SYMPTOM_OPTIONS here to avoid circular dependency
    const SYMPTOM_OPTIONS = [
      { value: "cramps", label: "Cramps" },
      { value: "acne", label: "Acne" },
      { value: "bloating", label: "Bloating" },
      { value: "headache", label: "Headache" },
      { value: "backache", label: "Back Pain" },
      { value: "breast_tenderness", label: "Breast Tenderness" },
      { value: "nausea", label: "Nausea" },
      { value: "fatigue", label: "Fatigue" },
      { value: "food_cravings", label: "Food Cravings" },
      { value: "mood_swings", label: "Mood Swings" },
      { value: "insomnia", label: "Insomnia" },
      { value: "hot_flashes", label: "Hot Flashes" },
      { value: "constipation", label: "Constipation" },
      { value: "diarrhea", label: "Diarrhea" },
      { value: "dizziness", label: "Dizziness" },
    ];

    return symptoms
      .map((symptom) => {
        const symptomOption = SYMPTOM_OPTIONS.find((s) => s.value === symptom);
        return symptomOption?.label || this.snakeToTitle(symptom);
      })
      .join(", ");
  }
}
