import React, { useState, useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Platform,
} from "react-native";
import { Calendar, DateData } from "react-native-calendars";
import { useRouter, useFocusEffect } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useCycleData } from "../hooks/useCycleData";
import { DateUtils, TextUtils } from "../utils/format";
import { THEME_COLORS } from "../types/constants";
import { SoftCard } from "../components/SoftCard";
import { SoftButton } from "../components/SoftButton";

export const CalendarScreen: React.FC = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const {
    entries,
    prediction,
    stats,
    isInFertileWindow,
    isPredictedPeriodDay,
    getEntryForDate,
    loading,
    refreshData,
  } = useCycleData();

  const [selectedDate, setSelectedDate] = useState<string>(
    DateUtils.getTodayString()
  );

  // Refresh data when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      refreshData();
    }, [refreshData])
  );

  // Generate calendar markings
  const markedDates = useMemo(() => {
    try {
      const marked: any = {};
      const today = DateUtils.getTodayString();

      // Mark ALL logged entries
      entries.forEach((entry) => {
        if (entry.isPeriodDay) {
          // Period days get red dots
          marked[entry.date] = {
            ...marked[entry.date],
            marked: true,
            dotColor: THEME_COLORS.period,
          };
        } else {
          // All other logged days get primary color dots
          marked[entry.date] = {
            ...marked[entry.date],
            marked: true,
            dotColor: THEME_COLORS.primary,
          };
        }
      });

      // Mark predicted fertile window
      if (prediction) {
        const fertileStart = prediction.fertileWindowStart;
        const fertileEnd = prediction.fertileWindowEnd;

        // Safety check: ensure dates are reasonable
        const daysDiff = DateUtils.getDaysDifference(fertileStart, fertileEnd);
        if (daysDiff >= 0 && daysDiff <= 10) {
          // Max 10 days for fertile window
          const fertileRange = DateUtils.getDateRange(fertileStart, fertileEnd);

          fertileRange.forEach((date) => {
            if (!marked[date]?.marked) {
              marked[date] = {
                ...marked[date],
                marked: true,
                dotColor: THEME_COLORS.fertile,
              };
            }
          });
        }

        // Mark predicted period days
        if (stats) {
          const periodStart = prediction.nextPeriodDate;
          const periodEnd = DateUtils.addDays(
            periodStart,
            stats.averagePeriodDuration - 1
          );

          // Safety check: ensure period duration is reasonable
          const periodDaysDiff = DateUtils.getDaysDifference(
            periodStart,
            periodEnd
          );
          if (periodDaysDiff >= 0 && periodDaysDiff <= 10) {
            // Max 10 days for period
            const periodRange = DateUtils.getDateRange(periodStart, periodEnd);

            periodRange.forEach((date) => {
              if (DateUtils.isFuture(date) || DateUtils.isToday(date)) {
                marked[date] = {
                  ...marked[date],
                  marked: true,
                  dotColor: THEME_COLORS.period + "80", // Semi-transparent for predictions
                };
              }
            });
          }
        }
      }

      // Mark selected date with light blue background - distinct from legend colors
      marked[selectedDate] = {
        ...marked[selectedDate],
        selected: true,
        selectedColor: "#E8F4FD", // Light blue background - different from all legend colors
      };

      // Mark today
      if (selectedDate !== today) {
        marked[today] = {
          ...marked[today],
          today: true,
        };
      }

      return marked;
    } catch (error) {
      console.error("Error generating calendar markings:", error);
      // Return empty markings to prevent crash
      return {};
    }
  }, [entries, prediction, stats, selectedDate]);

  const handleDayPress = (day: DateData) => {
    setSelectedDate(day.dateString);
  };

  const handleAddEntry = () => {
    router.push(`/logEntry?date=${selectedDate}`);
  };

  const selectedEntry = getEntryForDate(selectedDate);
  const isToday = DateUtils.isToday(selectedDate);
  const isFuture = DateUtils.isFuture(selectedDate);

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={{
        paddingTop: insets.top,
        paddingBottom:
          Platform.OS === "ios" ? insets.bottom + 100 : insets.bottom + 20, // Extra padding for iOS tab bar
      }}
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={refreshData} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.title}>Calendar</Text>
        <Text style={styles.subtitle}>Track your cycle</Text>
      </View>

      <Calendar
        current={selectedDate}
        onDayPress={handleDayPress}
        markedDates={markedDates}
        theme={{
          backgroundColor: THEME_COLORS.background,
          calendarBackground: THEME_COLORS.surface,
          textSectionTitleColor: THEME_COLORS.textSecondary,
          selectedDayBackgroundColor: "#E8F4FD", // Light blue background - distinct from legend colors
          selectedDayTextColor: "#2196F3", // Blue text for contrast
          todayTextColor: THEME_COLORS.primary,
          dayTextColor: THEME_COLORS.text,
          textDisabledColor: THEME_COLORS.textSecondary + "60",
          dotColor: THEME_COLORS.primary,
          selectedDotColor: "#2196F3", // Blue dot color for selected dates
          arrowColor: THEME_COLORS.primary,
          monthTextColor: THEME_COLORS.text,
          indicatorColor: THEME_COLORS.primary,
          textDayFontFamily: "System",
          textMonthFontFamily: "System",
          textDayHeaderFontFamily: "System",
          textDayFontWeight: "600",
          textMonthFontWeight: "600",
          textDayHeaderFontWeight: "600",
          textDayFontSize: 16,
          textMonthFontSize: 18,
          textDayHeaderFontSize: 14,
        }}
        style={styles.calendar}
      />

      <SoftCard delay={100}>
        <Text style={styles.legendTitle}>Calendar Guide</Text>
        <View style={styles.legendItems}>
          <View style={styles.legendItem}>
            <View
              style={[
                styles.legendColor,
                { backgroundColor: THEME_COLORS.period },
              ]}
            />
            <Text style={styles.legendText}>Period Days</Text>
          </View>
          <View style={styles.legendItem}>
            <View
              style={[
                styles.legendColor,
                { backgroundColor: THEME_COLORS.fertile },
              ]}
            />
            <Text style={styles.legendText}>Fertile Window</Text>
          </View>
          <View style={styles.legendItem}>
            <View
              style={[
                styles.legendColor,
                { backgroundColor: THEME_COLORS.primary },
              ]}
            />
            <Text style={styles.legendText}>Logged Data</Text>
          </View>
          <View style={styles.legendItem}>
            <View
              style={[
                styles.legendColor,
                { backgroundColor: THEME_COLORS.period + "80" },
              ]}
            />
            <Text style={styles.legendText}>Predictions</Text>
          </View>
        </View>
      </SoftCard>

      <SoftCard delay={200}>
        <Text style={styles.selectedDateTitle}>
          {DateUtils.formatWithDayName(selectedDate)}
          {isToday && " (Today)"}
        </Text>

        {selectedEntry ? (
          <View style={styles.entryInfo}>
            <View style={styles.entryRow}>
              <Text style={styles.entryLabel}>Period Day:</Text>
              <Text
                style={[
                  styles.entryValue,
                  selectedEntry.isPeriodDay && styles.periodText,
                ]}
              >
                {selectedEntry.isPeriodDay ? "Yes" : "No"}
              </Text>
            </View>

            {selectedEntry.isPeriodDay && selectedEntry.flowLevel && (
              <View style={styles.entryRow}>
                <Text style={styles.entryLabel}>Flow:</Text>
                <Text style={styles.entryValue}>{selectedEntry.flowLevel}</Text>
              </View>
            )}

            {selectedEntry.mood && (
              <View style={styles.entryRow}>
                <Text style={styles.entryLabel}>Mood:</Text>
                <Text style={styles.entryValue}>{selectedEntry.mood}</Text>
              </View>
            )}

            {selectedEntry.symptoms.length > 0 && (
              <View style={styles.entryRow}>
                <Text style={styles.entryLabel}>Symptoms:</Text>
                <Text style={styles.entryValue}>
                  {TextUtils.formatSymptoms(selectedEntry.symptoms)}
                </Text>
              </View>
            )}

            {selectedEntry.notes && (
              <View style={styles.entryRow}>
                <Text style={styles.entryLabel}>Notes:</Text>
                <Text style={styles.entryValue}>{selectedEntry.notes}</Text>
              </View>
            )}

            <SoftButton
              title="Edit Entry"
              onPress={handleAddEntry}
              variant="primary"
              size="medium"
              icon="✏️"
            />
          </View>
        ) : (
          <View style={styles.noEntryInfo}>
            <Text style={styles.noEntryText}>No data logged for this date</Text>

            {/* Show predictions for future dates */}
            {isFuture && prediction && (
              <View style={styles.predictionInfo}>
                {isPredictedPeriodDay(selectedDate) && (
                  <Text style={styles.predictionText}>
                    🔴 Predicted period day
                  </Text>
                )}
                {isInFertileWindow(selectedDate) && (
                  <Text style={styles.predictionText}>🌱 Fertile window</Text>
                )}
              </View>
            )}

            <SoftButton
              title={isFuture ? "Add Prediction" : "Log Data"}
              onPress={handleAddEntry}
              variant="accent"
              size="medium"
              icon={isFuture ? "🔮" : "🌸"}
            />
          </View>
        )}
      </SoftCard>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME_COLORS.background,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: THEME_COLORS.text,
  },
  subtitle: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    marginTop: 4,
  },
  calendar: {
    marginHorizontal: 16,
    marginBottom: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  legend: {
    margin: 16,
    padding: 16,
    backgroundColor: THEME_COLORS.surface,
    borderRadius: 12,
  },
  legendTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: THEME_COLORS.text,
    marginBottom: 12,
  },
  legendItems: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 16,
  },
  legendItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  legendText: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    fontWeight: "400",
  },
  selectedDateInfo: {
    margin: 16,
    padding: 16,
    backgroundColor: THEME_COLORS.surface,
    borderRadius: 12,
  },
  selectedDateTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: THEME_COLORS.text,
    marginBottom: 12,
  },
  entryInfo: {
    gap: 8,
  },
  entryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  entryLabel: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    fontWeight: "500",
  },
  entryValue: {
    fontSize: 14,
    color: THEME_COLORS.text,
    fontWeight: "600",
    flex: 1,
    textAlign: "right",
  },
  periodText: {
    color: THEME_COLORS.period,
  },
  editButton: {
    backgroundColor: THEME_COLORS.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 12,
  },
  editButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  noEntryInfo: {
    alignItems: "center",
  },
  noEntryText: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    textAlign: "center",
    marginBottom: 16,
  },
  predictionInfo: {
    marginBottom: 16,
  },
  predictionText: {
    fontSize: 14,
    color: THEME_COLORS.primary,
    textAlign: "center",
    marginBottom: 4,
  },
  addButton: {
    backgroundColor: THEME_COLORS.accent,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
  },
  addButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});
