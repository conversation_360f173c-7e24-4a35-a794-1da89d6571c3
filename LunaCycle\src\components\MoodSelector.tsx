import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { MoodType } from '../types/CycleEntry';
import { MOOD_OPTIONS, THEME_COLORS } from '../types/constants';

interface MoodSelectorProps {
  selectedMood?: MoodType;
  onMoodSelect: (mood: MoodType) => void;
  title?: string;
  style?: any;
}

export const MoodSelector: React.FC<MoodSelectorProps> = ({
  selectedMood,
  onMoodSelect,
  title = 'How are you feeling?',
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      <Text style={styles.title}>{title}</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.moodContainer}
      >
        {MOOD_OPTIONS.map((mood) => (
          <TouchableOpacity
            key={mood.value}
            style={[
              styles.moodButton,
              selectedMood === mood.value && styles.selectedMoodButton,
              { borderColor: mood.color },
              selectedMood === mood.value && { backgroundColor: mood.color + '20' },
            ]}
            onPress={() => onMoodSelect(mood.value)}
            activeOpacity={0.7}
          >
            <Text style={styles.moodEmoji}>{mood.emoji}</Text>
            <Text
              style={[
                styles.moodLabel,
                selectedMood === mood.value && styles.selectedMoodLabel,
              ]}
            >
              {mood.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME_COLORS.text,
    marginBottom: 12,
  },
  moodContainer: {
    paddingHorizontal: 4,
  },
  moodButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: THEME_COLORS.border,
    backgroundColor: THEME_COLORS.surface,
    minWidth: 80,
  },
  selectedMoodButton: {
    borderWidth: 2,
  },
  moodEmoji: {
    fontSize: 24,
    marginBottom: 4,
  },
  moodLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: THEME_COLORS.textSecondary,
    textAlign: 'center',
  },
  selectedMoodLabel: {
    color: THEME_COLORS.text,
    fontWeight: '600',
  },
});
