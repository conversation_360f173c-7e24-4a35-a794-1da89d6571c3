{"expo": {"name": "LocalOne Period & Cycle", "slug": "LocalOnePeriodCycle", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/appLogo.png", "scheme": "localone-period-cycle", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": false, "bundleIdentifier": "com.ha3d3n.LocalOnePeriodCycle", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.ha3d3n.LocalOnePeriodCycle"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "b754495f-cc67-4a65-8675-6a7df2f59a9f"}}, "owner": "ha3d3n"}}