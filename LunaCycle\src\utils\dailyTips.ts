import { CycleEntry, CycleStats, CyclePrediction } from "../types/CycleEntry";
import { DateUtils } from "./format";

export interface DailyTip {
  id: string;
  title: string;
  content: string;
  icon: string;
  category: "general" | "period" | "fertile" | "pms" | "wellness" | "tracking";
}

export class DailyTipsService {
  private static readonly GENERAL_TIPS: DailyTip[] = [
    {
      id: "getting_started",
      title: "Getting Started",
      content:
        "Welcome to LocalOne Period & Cycle! Log your symptoms daily for a few weeks to get personalized insights and accurate predictions.",
      icon: "🌟",
      category: "tracking",
    },
    {
      id: "logging_consistency",
      title: "Consistency is Key",
      content:
        "Daily logging helps create more accurate predictions. Recording your mood and symptoms on non-period days builds a complete picture of your unique cycle.",
      icon: "📝",
      category: "tracking",
    },
    {
      id: "hydration",
      title: "Stay Hydrated",
      content:
        "Drinking plenty of water throughout your cycle can help reduce bloating and improve energy levels. Aim for 8-10 glasses daily.",
      icon: "💧",
      category: "wellness",
    },
    {
      id: "sleep_importance",
      title: "Prioritize Sleep",
      content:
        "Quality sleep is crucial for hormone regulation. Try to maintain a consistent sleep schedule of 7-9 hours per night.",
      icon: "😴",
      category: "wellness",
    },
    {
      id: "exercise_benefits",
      title: "Move Your Body",
      content:
        "Regular exercise can help reduce period pain and improve mood. Even light walking or stretching can make a difference.",
      icon: "🏃‍♀️",
      category: "wellness",
    },
    {
      id: "nutrition_balance",
      title: "Balanced Nutrition",
      content:
        "Eating iron-rich foods, leafy greens, and complex carbs can help support your body throughout your cycle.",
      icon: "🥗",
      category: "wellness",
    },
    {
      id: "log_reminder",
      title: "Keep Up the Habit",
      content:
        "Regular logging helps maintain accurate cycle predictions. Try to log your symptoms daily for the best insights.",
      icon: "⏰",
      category: "tracking",
    },
    {
      id: "stress_management",
      title: "Manage Stress",
      content:
        "High stress can affect your cycle timing and symptoms. Try meditation, deep breathing, or gentle yoga to help manage stress levels.",
      icon: "🧘‍♀️",
      category: "wellness",
    },
    {
      id: "temperature_tracking",
      title: "Body Temperature Patterns",
      content:
        "Your basal body temperature slightly rises after ovulation. Tracking this can help identify your fertile window and confirm ovulation.",
      icon: "🌡️",
      category: "tracking",
    },
    {
      id: "cycle_variations",
      title: "Normal Variations",
      content:
        "It's normal for cycles to vary by a few days each month. Stress, travel, illness, and lifestyle changes can all affect timing.",
      icon: "📊",
      category: "general",
    },
    {
      id: "self_care",
      title: "Practice Self-Care",
      content:
        "Listen to your body's needs throughout your cycle. Some days call for rest, others for activity. Honor what feels right for you.",
      icon: "💆‍♀️",
      category: "wellness",
    },
  ];

  private static readonly PERIOD_TIPS: DailyTip[] = [
    {
      id: "period_comfort",
      title: "Comfort First",
      content:
        "Use a heating pad or warm bath to ease cramps. Gentle yoga poses like child's pose can also provide relief.",
      icon: "🛁",
      category: "period",
    },
    {
      id: "period_nutrition",
      title: "Iron-Rich Foods",
      content:
        "During your period, focus on iron-rich foods like spinach, lentils, and lean meats to replenish what you lose.",
      icon: "🥩",
      category: "period",
    },
    {
      id: "period_rest",
      title: "Listen to Your Body",
      content:
        "It's okay to take it easy during your period. Rest when you need to and don't push yourself too hard.",
      icon: "🛌",
      category: "period",
    },
    {
      id: "period_tracking",
      title: "Track Your Flow",
      content:
        "Note your flow intensity and any unusual changes. This information can be valuable for your healthcare provider.",
      icon: "📊",
      category: "tracking",
    },
    {
      id: "period_products",
      title: "Choose Your Products",
      content:
        "Experiment with different period products (pads, tampons, cups, discs) to find what works best for your body and lifestyle.",
      icon: "🩱",
      category: "period",
    },
    {
      id: "period_exercise",
      title: "Gentle Movement",
      content:
        "Light exercise during your period can help reduce cramps and boost mood. Try walking, swimming, or gentle stretching.",
      icon: "🚶‍♀️",
      category: "period",
    },
    {
      id: "period_hydration",
      title: "Extra Hydration",
      content:
        "You lose extra fluids during your period. Drink plenty of water and herbal teas to stay hydrated and reduce bloating.",
      icon: "💧",
      category: "period",
    },
    {
      id: "period_sleep",
      title: "Rest When Needed",
      content:
        "Your body works harder during menstruation. Don't feel guilty about needing extra sleep or taking naps during your period.",
      icon: "😴",
      category: "period",
    },
  ];

  private static readonly FERTILE_TIPS: DailyTip[] = [
    {
      id: "fertile_awareness",
      title: "Fertile Window",
      content:
        "You're in your fertile window! This is when conception is most likely if you're trying to conceive.",
      icon: "🌸",
      category: "fertile",
    },
    {
      id: "fertile_energy",
      title: "Peak Energy Time",
      content:
        "Many people feel most energetic during their fertile window. It's a great time for challenging workouts or important tasks.",
      icon: "⚡",
      category: "fertile",
    },
    {
      id: "fertile_symptoms",
      title: "Watch for Signs",
      content:
        "Notice changes in cervical mucus, slight temperature increases, or mild ovulation pain. These are normal fertile signs.",
      icon: "🔍",
      category: "tracking",
    },
    {
      id: "fertile_nutrition",
      title: "Fertility-Supporting Foods",
      content:
        "During your fertile window, focus on antioxidant-rich foods like berries, leafy greens, and nuts to support reproductive health.",
      icon: "🥬",
      category: "fertile",
    },
    {
      id: "ovulation_timing",
      title: "Ovulation Timing",
      content:
        "Ovulation typically occurs 12-16 days before your next period. Track patterns over several cycles for better accuracy.",
      icon: "📅",
      category: "fertile",
    },
  ];

  private static readonly PMS_TIPS: DailyTip[] = [
    {
      id: "pms_mood",
      title: "Mood Management",
      content:
        "PMS mood changes are normal. Try deep breathing, meditation, or talking to someone you trust when feeling overwhelmed.",
      icon: "🧘‍♀️",
      category: "pms",
    },
    {
      id: "pms_cravings",
      title: "Healthy Cravings",
      content:
        "Craving chocolate? Try dark chocolate with nuts or fruit. It satisfies cravings while providing beneficial nutrients.",
      icon: "🍫",
      category: "pms",
    },
    {
      id: "pms_bloating",
      title: "Reduce Bloating",
      content:
        "Limit salt intake and try herbal teas like peppermint or ginger to help reduce bloating and discomfort.",
      icon: "🍵",
      category: "pms",
    },
    {
      id: "pms_exercise",
      title: "Light Movement Helps",
      content:
        "Gentle exercise during PMS can help reduce symptoms. Try yoga, walking, or stretching to ease tension and boost mood.",
      icon: "🧘‍♀️",
      category: "pms",
    },
    {
      id: "pms_sleep",
      title: "Prioritize Rest",
      content:
        "PMS can disrupt sleep patterns. Create a calming bedtime routine and consider limiting caffeine in the afternoon.",
      icon: "🌙",
      category: "pms",
    },
    {
      id: "pms_support",
      title: "Seek Support",
      content:
        "Don't hesitate to reach out to friends, family, or healthcare providers when PMS symptoms feel overwhelming.",
      icon: "🤝",
      category: "pms",
    },
    {
      id: "pms_tracking",
      title: "Track PMS Patterns",
      content:
        "Notice which PMS symptoms are most common for you. This helps you prepare and manage symptoms more effectively.",
      icon: "📝",
      category: "pms",
    },
  ];

  static getTipOfTheDay(
    entries: CycleEntry[],
    prediction: CyclePrediction | null,
    stats: CycleStats | null
  ): DailyTip {
    const today = DateUtils.getTodayString();
    const todayEntry = entries.find((entry) => entry.date === today);

    // Determine current cycle phase
    const currentPhase = this.getCurrentCyclePhase(entries, prediction);

    // Get appropriate tips based on phase
    let availableTips: DailyTip[] = [];

    switch (currentPhase) {
      case "period":
        availableTips = [...this.PERIOD_TIPS, ...this.GENERAL_TIPS];
        break;
      case "fertile":
        availableTips = [...this.FERTILE_TIPS, ...this.GENERAL_TIPS];
        break;
      case "pms":
        availableTips = [...this.PMS_TIPS, ...this.GENERAL_TIPS];
        break;
      default:
        availableTips = this.GENERAL_TIPS;
    }

    // Add contextual tips based on user behavior
    // Note: getting_started tip is now in GENERAL_TIPS as a static tip

    // Add learning period tip for users with some data but not enough for full predictions
    if (
      entries.length >= 7 &&
      entries.length < 60 &&
      (!stats || stats.totalCycles < 3)
    ) {
      availableTips.unshift({
        id: "learning_period",
        title: "Building Your Profile",
        content:
          "LocalOne Period & Cycle is learning your unique patterns! After 2-3 complete cycles, you'll see much more accurate predictions and personalized insights.",
        icon: "🧠",
        category: "tracking",
      });
    }

    if (todayEntry && !todayEntry.mood && !todayEntry.symptoms.length) {
      availableTips.unshift({
        id: "complete_entry",
        title: "Complete Your Entry",
        content:
          "You've logged today, but consider adding your mood and any symptoms for more comprehensive tracking.",
        icon: "✨",
        category: "tracking",
      });
    }

    // Add tips for users who haven't logged recently
    // Note: log_reminder tip is now in GENERAL_TIPS as a static tip

    // Add tips based on cycle patterns
    if (stats && stats.averageCycleLength > 35) {
      availableTips.push({
        id: "long_cycles",
        title: "Long Cycles",
        content:
          "Longer cycles are normal for many people. If you're concerned about significant changes, consider discussing with your healthcare provider.",
        icon: "📅",
        category: "general",
      });
    }

    if (stats && stats.averageCycleLength < 21) {
      availableTips.push({
        id: "short_cycles",
        title: "Short Cycles",
        content:
          "Shorter cycles can be normal, but if this is a new pattern, it might be worth tracking for a few more months.",
        icon: "⚡",
        category: "general",
      });
    }

    // Use date-based selection for consistency (same tip for same day)
    const dayOfYear = this.getDayOfYear(new Date());
    const tipIndex = dayOfYear % availableTips.length;

    return availableTips[tipIndex];
  }

  private static getCurrentCyclePhase(
    entries: CycleEntry[],
    prediction: CyclePrediction | null
  ): "period" | "fertile" | "pms" | "other" {
    const today = DateUtils.getTodayString();
    const todayEntry = entries.find((entry) => entry.date === today);

    // Check if currently on period
    if (todayEntry?.isPeriodDay) {
      return "period";
    }

    // Check if in fertile window
    if (prediction) {
      const fertileStart = new Date(prediction.fertileWindowStart);
      const fertileEnd = new Date(prediction.fertileWindowEnd);
      const todayDate = new Date(today);

      if (todayDate >= fertileStart && todayDate <= fertileEnd) {
        return "fertile";
      }

      // Check if in PMS phase (5 days before predicted period)
      const nextPeriod = new Date(prediction.nextPeriodDate);
      const pmsStart = new Date(nextPeriod);
      pmsStart.setDate(pmsStart.getDate() - 5);

      if (todayDate >= pmsStart && todayDate < nextPeriod) {
        return "pms";
      }
    }

    return "other";
  }

  private static getDayOfYear(date: Date): number {
    const start = new Date(date.getFullYear(), 0, 0);
    const diff = date.getTime() - start.getTime();
    return Math.floor(diff / (1000 * 60 * 60 * 24));
  }

  static getAllTipsByCategory(category: DailyTip["category"]): DailyTip[] {
    const allTips = [
      ...this.GENERAL_TIPS,
      ...this.PERIOD_TIPS,
      ...this.FERTILE_TIPS,
      ...this.PMS_TIPS,
    ];

    return allTips.filter((tip) => tip.category === category);
  }

  static getRandomTip(): DailyTip {
    const allTips = [
      ...this.GENERAL_TIPS,
      ...this.PERIOD_TIPS,
      ...this.FERTILE_TIPS,
      ...this.PMS_TIPS,
    ];

    const randomIndex = Math.floor(Math.random() * allTips.length);
    return allTips[randomIndex];
  }
}
