import React from "react";
import Toast, {
  BaseToast,
  ErrorToast,
  InfoToast,
} from "react-native-toast-message";
import { THEME_COLORS } from "../types/constants";

// Custom toast configurations
const toastConfig = {
  success: (props: any) => (
    <BaseToast
      {...props}
      style={{
        borderLeftColor: THEME_COLORS.success,
        backgroundColor: THEME_COLORS.surface,
        minHeight: 80,
        maxHeight: 100,
        borderLeftWidth: 6,
        borderRadius: 12,
        marginHorizontal: 16,
        elevation: 8,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        width: "90%",
      }}
      contentContainerStyle={{
        paddingHorizontal: 16,
        paddingVertical: 12,
        flex: 1,
        justifyContent: "flex-start",
        alignItems: "stretch",
      }}
      text1Style={{
        fontSize: 16,
        fontWeight: "600",
        color: THEME_COLORS.text,
        marginBottom: 6,
        flexWrap: "wrap",
        textAlign: "left",
        lineHeight: 22,
      }}
      text2Style={{
        fontSize: 14,
        color: THEME_COLORS.textSecondary,
        lineHeight: 20,
        flexWrap: "wrap",
        flex: 1,
        textAlign: "left",
        marginTop: 4,
      }}
      text1NumberOfLines={1}
      text2NumberOfLines={2}
    />
  ),
  error: (props: any) => (
    <ErrorToast
      {...props}
      style={{
        borderLeftColor: THEME_COLORS.error,
        backgroundColor: THEME_COLORS.surface,
        minHeight: 80,
        maxHeight: 100,
        borderLeftWidth: 6,
        borderRadius: 12,
        marginHorizontal: 16,
        elevation: 8,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        width: "90%",
      }}
      contentContainerStyle={{
        paddingHorizontal: 16,
        paddingVertical: 12,
        flex: 1,
        justifyContent: "flex-start",
        alignItems: "stretch",
      }}
      text1Style={{
        fontSize: 16,
        fontWeight: "600",
        color: THEME_COLORS.text,
        marginBottom: 6,
        flexWrap: "wrap",
        textAlign: "left",
      }}
      text2Style={{
        fontSize: 14,
        color: THEME_COLORS.textSecondary,
        lineHeight: 20,
        flexWrap: "wrap",
        flex: 1,
        textAlign: "left",
        marginTop: 4,
      }}
      text1NumberOfLines={1}
      text2NumberOfLines={2}
    />
  ),
  info: (props: any) => (
    <InfoToast
      {...props}
      style={{
        borderLeftColor: THEME_COLORS.info,
        backgroundColor: THEME_COLORS.surface,
        minHeight: 80,
        maxHeight: 100,
        borderLeftWidth: 6,
        borderRadius: 12,
        marginHorizontal: 16,
        elevation: 8,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        width: "90%",
      }}
      contentContainerStyle={{
        paddingHorizontal: 16,
        paddingVertical: 12,
        flex: 1,
        justifyContent: "flex-start",
        alignItems: "stretch",
      }}
      text1Style={{
        fontSize: 16,
        fontWeight: "600",
        color: THEME_COLORS.text,
        marginBottom: 6,
        flexWrap: "wrap",
        textAlign: "left",
      }}
      text2Style={{
        fontSize: 14,
        color: THEME_COLORS.textSecondary,
        lineHeight: 20,
        flexWrap: "wrap",
        flex: 1,
        textAlign: "left",
        marginTop: 4,
      }}
      text1NumberOfLines={1}
      text2NumberOfLines={2}
    />
  ),
  warning: (props: any) => (
    <BaseToast
      {...props}
      style={{
        borderLeftColor: THEME_COLORS.warning,
        backgroundColor: THEME_COLORS.surface,
        minHeight: 80,
        maxHeight: 100,
        borderLeftWidth: 6,
        borderRadius: 12,
        marginHorizontal: 16,
        elevation: 8,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        width: "90%",
      }}
      contentContainerStyle={{
        paddingHorizontal: 16,
        paddingVertical: 12,
        flex: 1,
        justifyContent: "flex-start",
        alignItems: "stretch",
      }}
      text1Style={{
        fontSize: 16,
        fontWeight: "600",
        color: THEME_COLORS.text,
        marginBottom: 6,
        flexWrap: "wrap",
        textAlign: "left",
      }}
      text2Style={{
        fontSize: 14,
        color: THEME_COLORS.textSecondary,
        lineHeight: 20,
        flexWrap: "wrap",
        flex: 1,
        textAlign: "left",
        marginTop: 4,
      }}
      text1NumberOfLines={1}
      text2NumberOfLines={2}
    />
  ),
  settings: (props: any) => (
    <BaseToast
      {...props}
      style={{
        borderLeftColor: THEME_COLORS.primary,
        backgroundColor: THEME_COLORS.surface,
        minHeight: 80,
        maxHeight: 100,
        borderLeftWidth: 6,
        borderRadius: 12,
        marginHorizontal: 16,
        elevation: 8,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        width: "90%",
      }}
      contentContainerStyle={{
        paddingHorizontal: 16,
        paddingVertical: 12,
        flex: 1,
        justifyContent: "flex-start",
        alignItems: "stretch",
      }}
      text1Style={{
        fontSize: 16,
        fontWeight: "600",
        color: THEME_COLORS.text,
        marginBottom: 6,
        flexWrap: "wrap",
        textAlign: "left",
      }}
      text2Style={{
        fontSize: 14,
        color: THEME_COLORS.textSecondary,
        lineHeight: 20,
        flexWrap: "wrap",
        flex: 1,
        textAlign: "left",
        marginTop: 4,
      }}
      text1NumberOfLines={1}
      text2NumberOfLines={2}
    />
  ),
  delete: (props: any) => (
    <BaseToast
      {...props}
      style={{
        borderLeftColor: THEME_COLORS.error,
        backgroundColor: THEME_COLORS.surface,
        minHeight: 80,
        maxHeight: 100,
        borderLeftWidth: 6,
        borderRadius: 12,
        marginHorizontal: 16,
        elevation: 8,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        width: "90%",
      }}
      contentContainerStyle={{
        paddingHorizontal: 16,
        paddingVertical: 12,
        flex: 1,
        justifyContent: "flex-start",
        alignItems: "stretch",
      }}
      text1Style={{
        fontSize: 16,
        fontWeight: "600",
        color: THEME_COLORS.text,
        marginBottom: 6,
        flexWrap: "wrap",
        textAlign: "left",
      }}
      text2Style={{
        fontSize: 14,
        color: THEME_COLORS.textSecondary,
        lineHeight: 20,
        flexWrap: "wrap",
        flex: 1,
        textAlign: "left",
        marginTop: 4,
      }}
      text1NumberOfLines={1}
      text2NumberOfLines={2}
    />
  ),
};

export const ToastWrapper: React.FC = () => {
  return <Toast config={toastConfig} />;
};

// Toast utility functions
export const showToast = {
  success: (title: string, message?: string) => {
    Toast.show({
      type: "success",
      text1: title,
      text2: message,
      visibilityTime: 5000,
      autoHide: true,
      topOffset: 60,
    });
  },
  error: (title: string, message?: string) => {
    Toast.show({
      type: "error",
      text1: title,
      text2: message,
      visibilityTime: 6000,
      autoHide: true,
      topOffset: 60,
    });
  },
  info: (title: string, message?: string) => {
    Toast.show({
      type: "info",
      text1: title,
      text2: message,
      visibilityTime: 5000,
      autoHide: true,
      topOffset: 60,
    });
  },
  warning: (title: string, message?: string) => {
    Toast.show({
      type: "warning",
      text1: title,
      text2: message,
      visibilityTime: 5500,
      autoHide: true,
      topOffset: 60,
    });
  },
  reminder: (title: string, message?: string) => {
    Toast.show({
      type: "info",
      text1: `🌙 ${title}`,
      text2: message,
      visibilityTime: 7000,
      autoHide: true,
      topOffset: 60,
    });
  },
  settings: (title: string, message?: string) => {
    Toast.show({
      type: "settings",
      text1: `⚙️ ${title}`,
      text2: message,
      visibilityTime: 5000,
      autoHide: true,
      topOffset: 60,
    });
  },
  deleteConfirm: (title: string, message?: string) => {
    Toast.show({
      type: "delete",
      text1: title,
      text2: message,
      visibilityTime: 8000,
      autoHide: true,
      topOffset: 60,
    });
  },
};
