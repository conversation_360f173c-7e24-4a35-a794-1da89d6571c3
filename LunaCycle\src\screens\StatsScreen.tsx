import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Platform,
} from "react-native";
import { useRouter, useFocusEffect } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useCycleData } from "../hooks/useCycleData";
import { TextUtils } from "../utils/format";
import {
  THEME_COLORS,
  MOOD_OPTIONS,
  SYMPTOM_OPTIONS,
} from "../types/constants";
import { SoftCard } from "../components/SoftCard";
import { SoftButton } from "../components/SoftButton";
import { useCallback } from "react";

export const StatsScreen: React.FC = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { stats, analytics, loading, refreshData } = useCycleData();

  // Refresh data when screen comes into focus (e.g., returning from log entry)
  useFocusEffect(
    useCallback(() => {
      refreshData();
    }, [refreshData])
  );

  if (!stats || !analytics) {
    return (
      <ScrollView
        style={styles.container}
        contentContainerStyle={{
          paddingTop: insets.top,
          paddingBottom:
            Platform.OS === "ios" ? insets.bottom + 100 : insets.bottom + 20, // Extra padding for iOS tab bar
        }}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={refreshData} />
        }
      >
        <View style={styles.header}>
          <Text style={styles.title}>Statistics</Text>
          <Text style={styles.subtitle}>Your cycle insights</Text>
        </View>

        <View style={styles.emptyState}>
          <Text style={styles.emptyStateIcon}>📊</Text>
          <Text style={styles.emptyStateText}>Building Your Cycle Profile</Text>
          <Text style={styles.emptyStateSubtext}>
            LunaCycle learns your unique patterns over time. After tracking 2-3
            complete cycles, you'll see:
          </Text>
          <View style={styles.learningList}>
            <Text style={styles.learningItem}>✨ Personalized predictions</Text>
            <Text style={styles.learningItem}>📈 Detailed statistics</Text>
            <Text style={styles.learningItem}>🎯 Pattern recognition</Text>
            <Text style={styles.learningItem}>💡 Smart insights</Text>
          </View>
          <Text style={styles.encouragementText}>
            Keep logging daily - every entry makes your predictions more
            accurate!
          </Text>
        </View>
      </ScrollView>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={{
        paddingTop: insets.top,
        paddingBottom:
          Platform.OS === "ios" ? insets.bottom + 100 : insets.bottom + 20, // Extra padding for iOS tab bar
      }}
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={refreshData} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.title}>Statistics</Text>
        <Text style={styles.subtitle}>Your cycle insights</Text>
      </View>

      {/* Overview Stats */}
      <SoftCard delay={100}>
        <Text style={styles.cardTitle}>📊 Overview</Text>

        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{analytics.totalEntries}</Text>
            <Text style={styles.statLabel}>Total Entries</Text>
          </View>

          <View style={styles.statItem}>
            <Text style={styles.statValue}>{analytics.totalPeriodDays}</Text>
            <Text style={styles.statLabel}>Period Days Logged</Text>
          </View>

          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {analytics.cycleHistory.length}
            </Text>
            <Text style={styles.statLabel}>Cycles Tracked</Text>
          </View>
        </View>

        <SoftButton
          title="View All Data"
          onPress={() => router.push("/dataHistory")}
          variant="secondary"
          size="medium"
          icon="📋"
        />
      </SoftCard>

      {/* Cycle Averages */}
      <SoftCard delay={200}>
        <Text style={styles.cardTitle}>📈 Cycle Averages</Text>

        <View style={styles.averageRow}>
          <Text style={styles.averageLabel}>Average Cycle Length:</Text>
          <Text style={styles.averageValue}>
            {TextUtils.formatDuration(stats.averageCycleLength)}
          </Text>
        </View>

        <View style={styles.averageRow}>
          <Text style={styles.averageLabel}>Average Period Duration:</Text>
          <Text style={styles.averageValue}>
            {TextUtils.formatDuration(stats.averagePeriodDuration)}
          </Text>
        </View>
      </SoftCard>

      {/* Cycle History */}
      {analytics.cycleHistory.length > 0 && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Recent Cycles</Text>

          {analytics.cycleHistory
            .slice(-5)
            .reverse()
            .map((cycle) => (
              <View key={cycle.cycleNumber} style={styles.cycleHistoryItem}>
                <View style={styles.cycleHeader}>
                  <Text style={styles.cycleNumber}>
                    Cycle {cycle.cycleNumber}
                  </Text>
                  {cycle.length && (
                    <Text style={styles.cycleLength}>
                      {TextUtils.formatDuration(cycle.length)}
                    </Text>
                  )}
                </View>

                <Text style={styles.cycleDate}>
                  Started: {new Date(cycle.startDate).toLocaleDateString()}
                </Text>

                <Text style={styles.cyclePeriod}>
                  Period: {TextUtils.formatDuration(cycle.periodDuration)}
                </Text>
              </View>
            ))}
        </View>
      )}

      {/* Mood Frequency */}
      {analytics.moodFrequency.length > 0 && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Mood Frequency</Text>

          {analytics.moodFrequency
            .sort((a, b) => b.count - a.count)
            .slice(0, 5)
            .map((moodData) => {
              const moodOption = MOOD_OPTIONS.find(
                (m) => m.value === moodData.mood
              );
              return (
                <View key={moodData.mood} style={styles.frequencyItem}>
                  <View style={styles.frequencyLeft}>
                    <Text style={styles.frequencyEmoji}>
                      {moodOption?.emoji}
                    </Text>
                    <Text style={styles.frequencyLabel}>
                      {TextUtils.snakeToTitle(moodData.mood)}
                    </Text>
                  </View>

                  <View style={styles.frequencyRight}>
                    <Text style={styles.frequencyCount}>{moodData.count}</Text>
                    <Text style={styles.frequencyPercentage}>
                      {TextUtils.formatPercentage(moodData.percentage)}
                    </Text>
                  </View>
                </View>
              );
            })}
        </View>
      )}

      {/* Symptom Frequency */}
      {analytics.symptomFrequency.length > 0 && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Common Symptoms</Text>

          {analytics.symptomFrequency
            .sort((a, b) => b.count - a.count)
            .slice(0, 8)
            .map((symptomData) => {
              const symptomOption = SYMPTOM_OPTIONS.find(
                (s) => s.value === symptomData.symptom
              );
              return (
                <View key={symptomData.symptom} style={styles.frequencyItem}>
                  <View style={styles.frequencyLeft}>
                    <Text style={styles.frequencyEmoji}>
                      {symptomOption?.icon}
                    </Text>
                    <Text style={styles.frequencyLabel}>
                      {symptomOption?.label ||
                        TextUtils.snakeToTitle(symptomData.symptom)}
                    </Text>
                  </View>

                  <View style={styles.frequencyRight}>
                    <Text style={styles.frequencyCount}>
                      {symptomData.count}
                    </Text>
                    <Text style={styles.frequencyPercentage}>
                      {TextUtils.formatPercentage(symptomData.percentage)}
                    </Text>
                  </View>
                </View>
              );
            })}
        </View>
      )}

      {/* Insights */}
      <View style={styles.card}>
        <Text style={styles.cardTitle}>💡 Insights</Text>

        <View style={styles.insightsList}>
          {stats.averageCycleLength < 21 && (
            <Text style={styles.insightText}>
              • Your cycles are shorter than average (21-35 days). Consider
              discussing with a healthcare provider.
            </Text>
          )}

          {stats.averageCycleLength > 35 && (
            <Text style={styles.insightText}>
              • Your cycles are longer than average (21-35 days). This can be
              normal, but consider tracking for a few more months.
            </Text>
          )}

          {stats.averagePeriodDuration > 7 && (
            <Text style={styles.insightText}>
              • Your periods last longer than average (3-7 days). Consider
              discussing with a healthcare provider if this concerns you.
            </Text>
          )}

          {analytics.cycleHistory.length >= 3 && (
            <Text style={styles.insightText}>
              • Great job tracking! You have enough data for reliable
              predictions.
            </Text>
          )}

          {analytics.totalEntries < 30 && (
            <Text style={styles.insightText}>
              • Keep logging daily data to improve prediction accuracy and gain
              better insights.
            </Text>
          )}
        </View>
      </View>

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME_COLORS.background,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: THEME_COLORS.text,
  },
  subtitle: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    marginTop: 4,
  },
  emptyState: {
    alignItems: "center",
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyStateText: {
    fontSize: 20,
    fontWeight: "600",
    color: THEME_COLORS.text,
    textAlign: "center",
    marginBottom: 12,
  },
  emptyStateSubtext: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 22,
  },
  learningList: {
    alignItems: "flex-start",
    marginBottom: 20,
  },
  learningItem: {
    fontSize: 16,
    color: THEME_COLORS.text,
    marginBottom: 8,
    textAlign: "left",
  },
  encouragementText: {
    fontSize: 14,
    color: THEME_COLORS.primary,
    textAlign: "center",
    fontWeight: "500",
    fontStyle: "italic",
  },
  card: {
    backgroundColor: THEME_COLORS.surface,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 20,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: THEME_COLORS.text,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  statItem: {
    alignItems: "center",
  },
  statValue: {
    fontSize: 24,
    fontWeight: "bold",
    color: THEME_COLORS.primary,
  },
  statLabel: {
    fontSize: 12,
    color: THEME_COLORS.textSecondary,
    textAlign: "center",
    marginTop: 4,
  },
  averageRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  averageLabel: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    fontWeight: "500",
  },
  averageValue: {
    fontSize: 16,
    color: THEME_COLORS.text,
    fontWeight: "600",
  },
  cycleHistoryItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: THEME_COLORS.border,
  },
  cycleHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  cycleNumber: {
    fontSize: 16,
    fontWeight: "600",
    color: THEME_COLORS.text,
  },
  cycleLength: {
    fontSize: 14,
    color: THEME_COLORS.primary,
    fontWeight: "500",
  },
  cycleDate: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    marginBottom: 2,
  },
  cyclePeriod: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
  },
  frequencyItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
  },
  frequencyLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  frequencyEmoji: {
    fontSize: 18,
    marginRight: 12,
  },
  frequencyLabel: {
    fontSize: 16,
    color: THEME_COLORS.text,
    fontWeight: "500",
  },
  frequencyRight: {
    alignItems: "flex-end",
  },
  frequencyCount: {
    fontSize: 16,
    color: THEME_COLORS.primary,
    fontWeight: "600",
  },
  frequencyPercentage: {
    fontSize: 12,
    color: THEME_COLORS.textSecondary,
  },
  insightsList: {
    gap: 12,
  },
  insightText: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    lineHeight: 20,
  },
  bottomSpacing: {
    height: 40,
  },
});
