import {
  CycleEntry,
  CyclePrediction,
  CycleStats,
  CycleAnalytics,
} from "../types/CycleEntry";
import { DateUtils } from "./format";
import { CYCLE_DEFAULTS } from "../types/constants";

export interface CycleData {
  startDate: string;
  endDate?: string;
  length?: number;
  periodDuration: number;
}

export class CyclePredictionEngine {
  // Extract cycle data from period entries
  static extractCycles(entries: CycleEntry[]): CycleData[] {
    try {
      const today = DateUtils.getTodayString();

      // Filter and sort period entries by date, excluding future dates
      const periodEntries = entries
        .filter((entry) => entry.isPeriodDay && entry.date <= today)
        .sort(
          (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
        );

      if (periodEntries.length === 0) return [];

      const cycles: CycleData[] = [];
      let currentCycleStart: string | null = null;
      let currentPeriodDuration = 0;
      let lastPeriodDate: string | null = null;

      for (const entry of periodEntries) {
        const entryDate = entry.date;

        // Check if this is the start of a new cycle
        if (
          !lastPeriodDate ||
          DateUtils.getDaysDifference(lastPeriodDate, entryDate) > 1
        ) {
          // End previous cycle if it exists
          if (currentCycleStart && lastPeriodDate) {
            cycles.push({
              startDate: currentCycleStart,
              endDate: lastPeriodDate,
              periodDuration: currentPeriodDuration,
            });
          }

          // Start new cycle
          currentCycleStart = entryDate;
          currentPeriodDuration = 1;
        } else {
          // Continue current period
          currentPeriodDuration++;
        }

        lastPeriodDate = entryDate;
      }

      // Add the last cycle if it exists
      if (currentCycleStart && lastPeriodDate) {
        cycles.push({
          startDate: currentCycleStart,
          endDate: lastPeriodDate,
          periodDuration: currentPeriodDuration,
        });
      }

      // Calculate cycle lengths
      for (let i = 0; i < cycles.length - 1; i++) {
        const currentStart = cycles[i].startDate;
        const nextStart = cycles[i + 1].startDate;
        cycles[i].length = DateUtils.getDaysDifference(currentStart, nextStart);
      }

      return cycles;
    } catch (error) {
      console.error("Error extracting cycles:", error);
      return [];
    }
  }

  // Calculate cycle statistics
  static calculateStats(cycles: CycleData[]): CycleStats {
    if (cycles.length === 0) {
      return {
        averageCycleLength: CYCLE_DEFAULTS.AVERAGE_CYCLE_LENGTH,
        averagePeriodDuration: CYCLE_DEFAULTS.AVERAGE_PERIOD_DURATION,
      };
    }

    // Calculate averages
    const cycleLengths = cycles.filter((c) => c.length).map((c) => c.length!);
    const periodDurations = cycles.map((c) => c.periodDuration);

    const averageCycleLength =
      cycleLengths.length > 0
        ? Math.round(
            cycleLengths.reduce((sum, length) => sum + length, 0) /
              cycleLengths.length
          )
        : CYCLE_DEFAULTS.AVERAGE_CYCLE_LENGTH;

    const averagePeriodDuration = Math.round(
      periodDurations.reduce((sum, duration) => sum + duration, 0) /
        periodDurations.length
    );

    const lastCycle = cycles[cycles.length - 1];
    const lastPeriodStartDate = lastCycle?.startDate;

    let nextPredictedPeriodDate: string | undefined;
    let fertileWindowStart: string | undefined;
    let fertileWindowEnd: string | undefined;

    if (
      lastPeriodStartDate &&
      cycles.length >= CYCLE_DEFAULTS.MIN_CYCLES_FOR_PREDICTION
    ) {
      nextPredictedPeriodDate = DateUtils.addDays(
        lastPeriodStartDate,
        averageCycleLength
      );

      // Calculate fertile window (typically 6 days ending on ovulation day)
      const ovulationDay = DateUtils.subtractDays(
        nextPredictedPeriodDate,
        CYCLE_DEFAULTS.OVULATION_DAY_OFFSET
      );
      fertileWindowStart = DateUtils.subtractDays(
        ovulationDay,
        CYCLE_DEFAULTS.FERTILE_WINDOW_DAYS - 1
      );
      fertileWindowEnd = ovulationDay;
    }

    return {
      averageCycleLength,
      averagePeriodDuration,
      lastPeriodStartDate,
      nextPredictedPeriodDate,
      fertileWindowStart,
      fertileWindowEnd,
    };
  }

  // Generate detailed prediction with confidence level
  static generatePrediction(cycles: CycleData[]): CyclePrediction | null {
    try {
      if (cycles.length < CYCLE_DEFAULTS.MIN_CYCLES_FOR_PREDICTION) {
        return null;
      }

      const stats = this.calculateStats(cycles);

      if (
        !stats.nextPredictedPeriodDate ||
        !stats.fertileWindowStart ||
        !stats.fertileWindowEnd
      ) {
        return null;
      }

      // Validate dates are reasonable (not too far in the future)
      const today = DateUtils.getTodayString();
      const daysToPrediction = DateUtils.getDaysDifference(
        today,
        stats.nextPredictedPeriodDate
      );

      // If prediction is more than 60 days away, something is wrong
      if (daysToPrediction > 60 || daysToPrediction < -30) {
        console.warn(
          "Prediction date seems unreasonable:",
          stats.nextPredictedPeriodDate
        );
        return null;
      }

      // Calculate confidence based on cycle regularity
      const cycleLengths = cycles.filter((c) => c.length).map((c) => c.length!);
      const variance = this.calculateVariance(cycleLengths);

      let confidence: "low" | "medium" | "high";
      if (variance <= 2) {
        confidence = "high";
      } else if (variance <= 5) {
        confidence = "medium";
      } else {
        confidence = "low";
      }

      return {
        nextPeriodDate: stats.nextPredictedPeriodDate,
        fertileWindowStart: stats.fertileWindowStart,
        fertileWindowEnd: stats.fertileWindowEnd,
        confidence,
      };
    } catch (error) {
      console.error("Error generating prediction:", error);
      return null;
    }
  }

  // Calculate variance of cycle lengths
  private static calculateVariance(values: number[]): number {
    if (values.length === 0) return 0;

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map((val) => Math.pow(val - mean, 2));
    return Math.sqrt(
      squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length
    );
  }

  // Generate comprehensive analytics
  static generateAnalytics(entries: CycleEntry[]): CycleAnalytics {
    const cycles = this.extractCycles(entries);

    // Count mood frequency
    const moodCounts: Record<string, number> = {};
    const symptomCounts: Record<string, number> = {};

    entries.forEach((entry) => {
      if (entry.mood) {
        moodCounts[entry.mood] = (moodCounts[entry.mood] || 0) + 1;
      }

      entry.symptoms.forEach((symptom) => {
        symptomCounts[symptom] = (symptomCounts[symptom] || 0) + 1;
      });
    });

    const totalEntries = entries.length;
    const totalPeriodDays = entries.filter((e) => e.isPeriodDay).length;

    // Convert to frequency arrays
    const moodFrequency = Object.entries(moodCounts).map(([mood, count]) => ({
      mood: mood as any,
      count,
      percentage: (count / totalEntries) * 100,
    }));

    const symptomFrequency = Object.entries(symptomCounts).map(
      ([symptom, count]) => ({
        symptom: symptom as any,
        count,
        percentage: (count / totalEntries) * 100,
      })
    );

    // Create cycle history
    const cycleHistory = cycles.map((cycle, index) => ({
      cycleNumber: index + 1,
      startDate: cycle.startDate,
      endDate: cycle.endDate,
      length: cycle.length,
      periodDuration: cycle.periodDuration,
    }));

    return {
      totalEntries,
      totalPeriodDays,
      moodFrequency,
      symptomFrequency,
      cycleHistory,
    };
  }

  // Check if a date falls within the fertile window
  static isInFertileWindow(date: string, prediction: CyclePrediction): boolean {
    return (
      date >= prediction.fertileWindowStart &&
      date <= prediction.fertileWindowEnd
    );
  }

  // Check if a date is predicted to be a period day
  static isPredictedPeriodDay(
    date: string,
    prediction: CyclePrediction,
    averagePeriodDuration: number
  ): boolean {
    const periodStart = prediction.nextPeriodDate;
    const periodEnd = DateUtils.addDays(periodStart, averagePeriodDuration - 1);
    return date >= periodStart && date <= periodEnd;
  }

  // Get days until next period
  static getDaysUntilNextPeriod(prediction: CyclePrediction): number {
    const today = DateUtils.getTodayString();
    return DateUtils.getDaysDifference(today, prediction.nextPeriodDate);
  }

  // Get current cycle day (day since last period started)
  static getCurrentCycleDay(cycles: CycleData[]): number | null {
    if (cycles.length === 0) return null;

    const lastCycle = cycles[cycles.length - 1];
    const today = DateUtils.getTodayString();

    const daysDifference = DateUtils.getDaysDifference(
      lastCycle.startDate,
      today
    );

    // If the last period start date is in the future, return null
    // This can happen if there's incorrect data or future dates logged by mistake
    if (daysDifference < 0) {
      return null;
    }

    return daysDifference + 1;
  }

  // Predict multiple future cycles
  static predictFutureCycles(
    cycles: CycleData[],
    numberOfCycles: number = 3
  ): CyclePrediction[] {
    const stats = this.calculateStats(cycles);
    const predictions: CyclePrediction[] = [];

    if (
      !stats.nextPredictedPeriodDate ||
      cycles.length < CYCLE_DEFAULTS.MIN_CYCLES_FOR_PREDICTION
    ) {
      return predictions;
    }

    let currentPredictionDate = stats.nextPredictedPeriodDate;

    for (let i = 0; i < numberOfCycles; i++) {
      const fertileWindowStart = DateUtils.subtractDays(
        DateUtils.subtractDays(
          currentPredictionDate,
          CYCLE_DEFAULTS.OVULATION_DAY_OFFSET
        ),
        CYCLE_DEFAULTS.FERTILE_WINDOW_DAYS - 1
      );
      const fertileWindowEnd = DateUtils.subtractDays(
        currentPredictionDate,
        CYCLE_DEFAULTS.OVULATION_DAY_OFFSET
      );

      // Confidence decreases for future predictions
      let confidence: "low" | "medium" | "high" = "medium";
      if (i === 0) confidence = "high";
      if (i >= 2) confidence = "low";

      predictions.push({
        nextPeriodDate: currentPredictionDate,
        fertileWindowStart,
        fertileWindowEnd,
        confidence,
      });

      // Move to next cycle
      currentPredictionDate = DateUtils.addDays(
        currentPredictionDate,
        stats.averageCycleLength
      );
    }

    return predictions;
  }
}
