import React, { useState, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from "react-native";
import { useRouter, useFocusEffect } from "expo-router";
import { useCycleData } from "../hooks/useCycleData";
import { DateUtils, TextUtils } from "../utils/format";
import {
  THEME_COLORS,
  MOOD_OPTIONS,
  SYMPTOM_OPTIONS,
} from "../types/constants";
import { SoftCard } from "../components/SoftCard";
import { CycleEntry } from "../types/CycleEntry";

export const DataHistoryScreen: React.FC = () => {
  const router = useRouter();
  const { entries, loading, refreshData } = useCycleData();
  const [sortOrder, setSortOrder] = useState<"newest" | "oldest">("newest");

  // Refresh data when screen comes into focus (e.g., returning from deleting an entry)
  useFocusEffect(
    useCallback(() => {
      // Force immediate refresh to show deleted entries
      refreshData();
    }, [refreshData])
  );

  // Sort entries based on selected order
  const sortedEntries = [...entries].sort((a, b) => {
    const dateA = new Date(a.date).getTime();
    const dateB = new Date(b.date).getTime();
    return sortOrder === "newest" ? dateB - dateA : dateA - dateB;
  });

  const handleEntryPress = (entry: CycleEntry) => {
    router.push(`/logEntry?date=${entry.date}&existingEntry=true`);
  };

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === "newest" ? "oldest" : "newest");
  };

  const getMoodDisplay = (mood?: string) => {
    if (!mood) return null;
    const moodOption = MOOD_OPTIONS.find((m) => m.value === mood);
    return moodOption ? `${moodOption.emoji} ${moodOption.label}` : mood;
  };

  const getSymptomsDisplay = (symptoms: string[]) => {
    if (symptoms.length === 0) return "None";
    if (symptoms.length <= 3) {
      return symptoms
        .map((symptom) => {
          const symptomOption = SYMPTOM_OPTIONS.find(
            (s) => s.value === symptom
          );
          return symptomOption?.icon || "•";
        })
        .join(" ");
    }
    return `${symptoms.length} symptoms`;
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={refreshData} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.title}>Data History</Text>
        <Text style={styles.subtitle}>All your logged entries</Text>
      </View>

      {/* Sort Controls */}
      <SoftCard delay={0}>
        <View style={styles.sortContainer}>
          <Text style={styles.sortLabel}>Sort by date:</Text>
          <TouchableOpacity style={styles.sortButton} onPress={toggleSortOrder}>
            <Text style={styles.sortButtonText}>
              {sortOrder === "newest" ? "Newest First ↓" : "Oldest First ↑"}
            </Text>
          </TouchableOpacity>
        </View>
        <Text style={styles.totalEntries}>Total entries: {entries.length}</Text>
      </SoftCard>

      {/* Entries List */}
      {sortedEntries.length === 0 ? (
        <SoftCard delay={100}>
          <View style={styles.noDataContainer}>
            <Text style={styles.noDataText}>No data logged yet</Text>
            <Text style={styles.noDataSubtext}>
              Start logging your cycle data to see it here
            </Text>
          </View>
        </SoftCard>
      ) : (
        sortedEntries.map((entry, index) => (
          <SoftCard key={entry.date} delay={100 + index * 50}>
            <TouchableOpacity
              style={styles.entryCard}
              onPress={() => handleEntryPress(entry)}
            >
              <View style={styles.entryHeader}>
                <View style={styles.entryDateContainer}>
                  <Text style={styles.entryDate}>
                    {DateUtils.formatWithDayName(entry.date)}
                  </Text>
                  {DateUtils.isToday(entry.date) && (
                    <Text style={styles.todayBadge}>Today</Text>
                  )}
                </View>
                <Text style={styles.editIcon}>✏️</Text>
              </View>

              <View style={styles.entryContent}>
                {/* Period Status */}
                <View style={styles.entryRow}>
                  <Text style={styles.entryLabel}>Period:</Text>
                  <Text
                    style={[
                      styles.entryValue,
                      entry.isPeriodDay && styles.periodValue,
                    ]}
                  >
                    {entry.isPeriodDay
                      ? `Yes ${
                          entry.flowLevel
                            ? `(${TextUtils.snakeToTitle(entry.flowLevel)})`
                            : ""
                        }`
                      : "No"}
                  </Text>
                </View>

                {/* Mood */}
                <View style={styles.entryRow}>
                  <Text style={styles.entryLabel}>Mood:</Text>
                  <Text style={styles.entryValue}>
                    {getMoodDisplay(entry.mood) || "Not logged"}
                  </Text>
                </View>

                {/* Symptoms */}
                <View style={styles.entryRow}>
                  <Text style={styles.entryLabel}>Symptoms:</Text>
                  <Text style={styles.entryValue}>
                    {getSymptomsDisplay(entry.symptoms)}
                  </Text>
                </View>

                {/* Notes */}
                {entry.notes && (
                  <View style={styles.entryRow}>
                    <Text style={styles.entryLabel}>Notes:</Text>
                    <Text style={styles.entryValue} numberOfLines={2}>
                      {entry.notes}
                    </Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>
          </SoftCard>
        ))
      )}

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME_COLORS.background,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: THEME_COLORS.text,
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
  },
  sortContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 10,
  },
  sortLabel: {
    fontSize: 16,
    color: THEME_COLORS.text,
    fontWeight: "500",
  },
  sortButton: {
    backgroundColor: THEME_COLORS.primary,
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  sortButtonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "500",
  },
  totalEntries: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    textAlign: "center",
  },
  noDataContainer: {
    alignItems: "center",
    padding: 20,
  },
  noDataText: {
    fontSize: 18,
    color: THEME_COLORS.text,
    fontWeight: "500",
    marginBottom: 5,
  },
  noDataSubtext: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    textAlign: "center",
  },
  entryCard: {
    padding: 0,
  },
  entryHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 15,
  },
  entryDateContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  entryDate: {
    fontSize: 18,
    fontWeight: "600",
    color: THEME_COLORS.text,
  },
  todayBadge: {
    backgroundColor: THEME_COLORS.accent,
    color: "white",
    fontSize: 12,
    fontWeight: "500",
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 10,
  },
  editIcon: {
    fontSize: 16,
    opacity: 0.6,
  },
  entryContent: {
    gap: 8,
  },
  entryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  entryLabel: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    fontWeight: "500",
    flex: 1,
  },
  entryValue: {
    fontSize: 14,
    color: THEME_COLORS.text,
    flex: 2,
    textAlign: "right",
  },
  periodValue: {
    color: THEME_COLORS.period,
    fontWeight: "500",
  },
  bottomSpacing: {
    height: 20,
  },
});
