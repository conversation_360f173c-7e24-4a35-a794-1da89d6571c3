import { MoodType, SymptomType } from "./CycleEntry";

// Mood options with display labels and icons
export const MOOD_OPTIONS: Array<{
  value: MoodType;
  label: string;
  emoji: string;
  color: string;
}> = [
  { value: "happy", label: "Happy", emoji: "😊", color: "#FFD700" },
  { value: "sad", label: "Sad", emoji: "😢", color: "#4169E1" },
  { value: "angry", label: "Angry", emoji: "😠", color: "#DC143C" },
  { value: "anxious", label: "Anxious", emoji: "😰", color: "#FF6347" },
  { value: "calm", label: "Calm", emoji: "😌", color: "#98FB98" },
  { value: "energetic", label: "Energetic", emoji: "⚡", color: "#FFA500" },
  { value: "tired", label: "Tired", emoji: "😴", color: "#708090" },
  { value: "irritable", label: "Irritable", emoji: "😤", color: "#FF4500" },
  { value: "emotional", label: "Emotional", emoji: "🥺", color: "#DA70D6" },
  { value: "neutral", label: "Neutral", emoji: "😐", color: "#A9A9A9" },
];

// Symptom options with display labels and categories
export const SYMPTOM_OPTIONS: Array<{
  value: SymptomType;
  label: string;
  category: "physical" | "digestive" | "emotional" | "other";
  icon: string;
}> = [
  { value: "cramps", label: "Cramps", category: "physical", icon: "🤕" },
  { value: "acne", label: "Acne", category: "physical", icon: "🔴" },
  { value: "bloating", label: "Bloating", category: "digestive", icon: "🎈" },
  { value: "headache", label: "Headache", category: "physical", icon: "🤯" },
  { value: "backache", label: "Back Pain", category: "physical", icon: "🦴" },
  {
    value: "breast_tenderness",
    label: "Breast Tenderness",
    category: "physical",
    icon: "💔",
  },
  { value: "nausea", label: "Nausea", category: "digestive", icon: "🤢" },
  { value: "fatigue", label: "Fatigue", category: "physical", icon: "😪" },
  {
    value: "food_cravings",
    label: "Food Cravings",
    category: "other",
    icon: "🍫",
  },
  {
    value: "mood_swings",
    label: "Mood Swings",
    category: "emotional",
    icon: "🎭",
  },
  { value: "insomnia", label: "Insomnia", category: "other", icon: "🌙" },
  {
    value: "hot_flashes",
    label: "Hot Flashes",
    category: "physical",
    icon: "🔥",
  },
  {
    value: "constipation",
    label: "Constipation",
    category: "digestive",
    icon: "🚫",
  },
  { value: "diarrhea", label: "Diarrhea", category: "digestive", icon: "💧" },
  { value: "dizziness", label: "Dizziness", category: "physical", icon: "💫" },
];

// Flow level options
export const FLOW_LEVEL_OPTIONS = [
  { value: "light" as const, label: "Light", color: "#FFD3E1", icon: "💧" },
  { value: "medium" as const, label: "Medium", color: "#FF9AA2", icon: "💧💧" },
  { value: "heavy" as const, label: "Heavy", color: "#FF6B7A", icon: "💧💧💧" },
];

// App theme colors - Soft and calming palette (Light theme fallback)
// Use useThemeColors() hook instead for dynamic theming
export const THEME_COLORS = {
  primary: "#B19CD9", // Soft lavender
  secondary: "#E6D7FF", // Light lavender
  accent: "#FF9AA2", // Soft coral
  background: "#FEFEFE", // Pure white
  surface: "#F9F7FF", // Very light lavender tint
  text: "#4A4A4A", // Soft dark gray
  textSecondary: "#8B8B8B", // Medium gray
  border: "#E8E4F0", // Light lavender border
  error: "#FF9AA2", // Soft coral for errors
  success: "#B8E6B8", // Soft mint green
  warning: "#FFD3A5", // Soft peach
  info: "#A8D8EA", // Soft sky blue
  period: "#FF9AA2", // Soft coral for period
  fertile: "#C7CEEA", // Soft periwinkle
  ovulation: "#FFD3A5", // Soft peach for ovulation
};

// Storage keys
export const STORAGE_KEYS = {
  CYCLE_ENTRIES: "@localone_period_cycle_entries",
  USER_SETTINGS: "@localone_period_cycle_settings",
  LAST_BACKUP: "@localone_period_cycle_last_backup",
} as const;

// Default cycle parameters
export const CYCLE_DEFAULTS = {
  AVERAGE_CYCLE_LENGTH: 28,
  AVERAGE_PERIOD_DURATION: 5,
  FERTILE_WINDOW_DAYS: 6,
  OVULATION_DAY_OFFSET: 14, // Days before next period
  MIN_CYCLES_FOR_PREDICTION: 3,
} as const;
