import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { THEME_COLORS } from '../types/constants';

interface TabIconProps {
  emoji: string;
  focused: boolean;
  color: string;
}

export const TabIcon: React.FC<TabIconProps> = ({ emoji, focused, color }) => {
  return (
    <View style={[styles.container, focused && styles.focused]}>
      <Text style={[styles.emoji, { opacity: focused ? 1 : 0.6 }]}>
        {emoji}
      </Text>
      {focused && <View style={[styles.indicator, { backgroundColor: color }]} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    minHeight: 32,
  },
  focused: {
    transform: [{ scale: 1.1 }],
  },
  emoji: {
    fontSize: 24,
    textAlign: 'center',
  },
  indicator: {
    width: 4,
    height: 4,
    borderRadius: 2,
    marginTop: 2,
  },
});
