export type RootTabParamList = {
  home: undefined;
  calendar: undefined;
  stats: undefined;
  settings: undefined;
};

export type RootStackParamList = {
  main: undefined;
  logEntry: {
    date: string;
    existingEntry?: string; // entry ID if editing
  };
};

export type TabScreenProps<T extends keyof RootTabParamList> = {
  navigation: any;
  route: {
    params: RootTabParamList[T];
  };
};

export type StackScreenProps<T extends keyof RootStackParamList> = {
  navigation: any;
  route: {
    params: RootStackParamList[T];
  };
};
