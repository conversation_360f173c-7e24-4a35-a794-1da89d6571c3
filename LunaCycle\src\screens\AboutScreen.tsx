﻿import React from "react";
import {
  View,
  Text,
  ScrollView,
  Linking,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import { useRouter } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { THEME_COLORS } from "../types/constants";
import { SoftCard } from "../components/SoftCard";
import { SoftButton } from "../components/SoftButton";

// App info data
const appInfo = [
  {
    name: "LocalOne Period & Cycle",
    description:
      "A privacy-first period tracking app that keeps all your data on your device. Track your cycle, symptoms, and mood with beautiful insights.",
    icon: "🩸",
    color: THEME_COLORS.primary,
    url: "https://apps.apple.com/us/app/localone-period-cycle/id123456789", // Replace with actual URL
    pricing: "Free",
  },
  {
    name: "LocalOne Crosswords",
    description:
      "A lightweight, privacy-focused iOS crossword puzzle app that provides users with a new crossword puzzle every day.",
    icon: "🧩",
    color: "#3498db",
    url: "https://apps.apple.com/us/app/dailycross/id6742525547",
    pricing: "$1 — forever",
  },
  {
    name: "BounceSmash",
    description:
      "Unleash your skills and conquer challenges in thrilling ball-bouncing action where precision and strategy lead to victory.",
    icon: "⚽",
    color: "#f39c12",
    url: "https://apps.apple.com/us/app/bouncesmash/id6740758269",
    pricing: "$1 — forever",
  },
  {
    name: "GlobeWhiz",
    description:
      "Test your geography skills with GlobeWhiz, the ultimate tool to match countries and their capitals!",
    icon: "🌍",
    color: "#27ae60",
    url: "https://apps.apple.com/us/app/globewhiz/id6740607020",
    pricing: "$1 — forever",
  },
  {
    name: "LocalOne Gym Pics",
    description:
      "A no-frills Gym Pic Tracker. Snap progress photos, auto-timestamp them, and keep them right on your device—no cloud servers involved.",
    icon: "📸",
    color: "#6366F1",
    url: "https://apps.apple.com/us/app/gympic-progress-tracker/id6740142163",
    pricing: "$1 — forever",
  },
  {
    name: "LocalOne Habit",
    description:
      "Embark on your journey to lasting habits with this elegantly designed habit tracker that makes building better habits a delightful experience.",
    icon: "📈",
    color: "#e74c3c",
    url: "https://apps.apple.com/us/app/localstreak-stay-motivated/id6741501550",
    pricing: "$1 — forever",
  },
  {
    name: "LocalOne Vault",
    description:
      "LocalOneVault is the lightweight, 100% offline photo vault that keeps your private images exactly where they belong: on your device",
    icon: "🔐",
    color: "#e74c3c",
    url: "https://apps.apple.com/us/app/localone-vault/id6748964835",
    pricing: "$1 — forever",
  },
];

export const AboutScreen: React.FC = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();

  const handleRateApp = () => {
    Linking.openURL(
      "https://apps.apple.com/us/app/localone-period-cycle/id123456789?action=write-review" // Replace with actual URL
    );
  };

  const handleContactUs = () => {
    Linking.openURL("mailto:<EMAIL>");
  };

  const handleAppStore = (url: string) => {
    Linking.openURL(url);
  };

  const handlePrivacyPolicy = () => {
    Linking.openURL("https://localonelabs.com/pages/privacy-policy");
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={{
          paddingTop: insets.top,
          paddingBottom: insets.bottom + 20, // Extra padding for content
        }}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          <SoftCard delay={0}>
            <View style={styles.mainCard}>
              <Text style={styles.title}>LocalOne Period & Cycle</Text>
              <Text style={styles.version}>Version 1.0.0</Text>
              <Text style={styles.description}>
                A privacy-first period tracking app that keeps all your data on
                your device. Track your cycle, symptoms, and mood with beautiful
                insights and predictions.
              </Text>
            </View>
          </SoftCard>

          <SoftCard delay={100}>
            <View style={styles.card}>
              <Text style={styles.sectionTitle}>Support Us</Text>
              <Text style={styles.sectionDescription}>
                If you enjoy using LocalOne Period & Cycle, please consider
                supporting our work.
              </Text>

              <View style={styles.buttonRow}>
                <SoftButton
                  title="Rate This App"
                  onPress={handleRateApp}
                  style={styles.buttonHalf}
                  variant="primary"
                />

                <SoftButton
                  title="Contact Us"
                  onPress={handleContactUs}
                  style={styles.buttonHalf}
                  variant="primary"
                />
              </View>
            </View>
          </SoftCard>

          <SoftCard delay={200}>
            <View style={styles.card}>
              <Text style={styles.sectionTitle}>Privacy First</Text>
              <Text style={styles.privacyText}>
                🔒 All your data stays on your device{"\n"}
                🚫 No cloud storage or syncing{"\n"}
                👤 No user accounts required{"\n"}
                📱 Works completely offline{"\n"}
                🛡️ Your privacy is our priority
              </Text>
            </View>
          </SoftCard>

          <SoftCard delay={300}>
            <View style={styles.card}>
              <Text style={styles.sectionTitle}>Our Other Apps</Text>
              <Text style={styles.sectionDescription}>
                We build privacy-focused apps that work fully offline and
                respect your data.
              </Text>

              {appInfo.slice(1).map((app, index) => (
                <View key={app.name} style={styles.appCard}>
                  <View style={styles.appHeader}>
                    <View
                      style={[
                        styles.appIconContainer,
                        { backgroundColor: app.color },
                      ]}
                    >
                      <Text style={styles.appIcon}>{app.icon}</Text>
                    </View>
                    <View style={styles.appInfo}>
                      <Text style={styles.appName}>{app.name}</Text>
                      <Text style={styles.appPricing}>{app.pricing}</Text>
                    </View>
                  </View>

                  <Text style={styles.appDescription}>{app.description}</Text>

                  <SoftButton
                    title="View on App Store"
                    onPress={() => handleAppStore(app.url)}
                    style={styles.appStoreButton}
                    variant="primary"
                  />
                </View>
              ))}
            </View>
          </SoftCard>

          <SoftCard delay={400}>
            <View style={styles.card}>
              <Text style={styles.sectionTitle}>Legal</Text>

              <TouchableOpacity
                onPress={handlePrivacyPolicy}
                style={styles.legalLink}
              >
                <Text style={styles.legalLinkText}>Privacy Policy</Text>
                <Text style={styles.legalArrow}>›</Text>
              </TouchableOpacity>
            </View>
          </SoftCard>

          <View style={styles.footer}>
            <Text style={styles.footerText}>Made with ❤️ by LocalOneLabs</Text>
            <Text style={styles.footerSubtext}>
              All data stays on your device
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME_COLORS.background,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingTop: 60,
    borderBottomWidth: 1,
    borderBottomColor: THEME_COLORS.border,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 20,
    backgroundColor: THEME_COLORS.surface,
  },
  backButtonText: {
    fontSize: 24,
    color: THEME_COLORS.text,
    fontWeight: "300",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: THEME_COLORS.text,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  mainCard: {
    alignItems: "center",
    paddingVertical: 20,
  },
  card: {
    padding: 4,
  },
  title: {
    fontSize: 32,
    fontWeight: "300",
    color: THEME_COLORS.primary,
    marginBottom: 8,
    textAlign: "center",
  },
  version: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    marginBottom: 16,
    textAlign: "center",
  },
  description: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    lineHeight: 24,
    textAlign: "center",
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: THEME_COLORS.text,
    marginBottom: 12,
  },
  sectionDescription: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    marginBottom: 20,
    lineHeight: 20,
  },
  buttonRow: {
    flexDirection: "row",
    gap: 12,
  },
  buttonHalf: {
    flex: 1,
  },
  privacyText: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    lineHeight: 24,
  },
  appCard: {
    backgroundColor: THEME_COLORS.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: THEME_COLORS.border,
  },
  appHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  appIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  appIcon: {
    fontSize: 24,
    color: "white",
  },
  appInfo: {
    flex: 1,
  },
  appName: {
    fontSize: 16,
    fontWeight: "600",
    color: THEME_COLORS.text,
  },
  appPricing: {
    fontSize: 12,
    color: THEME_COLORS.textSecondary,
    marginTop: 2,
  },
  appDescription: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    lineHeight: 20,
    marginBottom: 16,
  },
  appStoreButton: {
    marginTop: 8,
  },
  legalLink: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: THEME_COLORS.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: THEME_COLORS.border,
  },
  legalLinkText: {
    fontSize: 16,
    color: THEME_COLORS.text,
  },
  legalArrow: {
    fontSize: 20,
    color: THEME_COLORS.textSecondary,
  },
  footer: {
    alignItems: "center",
    paddingVertical: 40,
  },
  footerText: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    marginBottom: 8,
  },
  footerSubtext: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
  },
});
