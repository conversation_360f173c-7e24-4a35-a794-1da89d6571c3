export type FlowLevel = 'light' | 'medium' | 'heavy';

export type MoodType = 
  | 'happy' 
  | 'sad' 
  | 'angry' 
  | 'anxious' 
  | 'calm' 
  | 'energetic' 
  | 'tired' 
  | 'irritable' 
  | 'emotional' 
  | 'neutral';

export type SymptomType = 
  | 'cramps'
  | 'acne'
  | 'bloating'
  | 'headache'
  | 'backache'
  | 'breast_tenderness'
  | 'nausea'
  | 'fatigue'
  | 'food_cravings'
  | 'mood_swings'
  | 'insomnia'
  | 'hot_flashes'
  | 'constipation'
  | 'diarrhea'
  | 'dizziness';

export interface CycleEntry {
  id: string;
  date: string; // ISO date string (YYYY-MM-DD)
  isPeriodDay: boolean;
  flowLevel?: FlowLevel;
  mood?: MoodType;
  symptoms: SymptomType[];
  notes?: string;
  createdAt: string; // ISO datetime string
  updatedAt: string; // ISO datetime string
}

export interface CycleStats {
  averageCycleLength: number;
  averagePeriodDuration: number;
  lastPeriodStartDate?: string;
  nextPredictedPeriodDate?: string;
  fertileWindowStart?: string;
  fertileWindowEnd?: string;
}

export interface CyclePrediction {
  nextPeriodDate: string;
  fertileWindowStart: string;
  fertileWindowEnd: string;
  confidence: 'low' | 'medium' | 'high';
}

export interface MoodSymptomFrequency {
  mood: MoodType;
  count: number;
  percentage: number;
}

export interface SymptomFrequency {
  symptom: SymptomType;
  count: number;
  percentage: number;
}

export interface CycleAnalytics {
  totalEntries: number;
  totalPeriodDays: number;
  moodFrequency: MoodSymptomFrequency[];
  symptomFrequency: SymptomFrequency[];
  cycleHistory: {
    cycleNumber: number;
    startDate: string;
    endDate?: string;
    length?: number;
    periodDuration: number;
  }[];
}

// UI-related types
export interface CalendarMarking {
  selected?: boolean;
  marked?: boolean;
  selectedColor?: string;
  dotColor?: string;
  activeOpacity?: number;
  disabled?: boolean;
  disableTouchEvent?: boolean;
}

export interface ToastConfig {
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message?: string;
  duration?: number;
}
