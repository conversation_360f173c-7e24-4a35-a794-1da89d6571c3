import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from "react-native";
import { useRouter, useFocusEffect } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useCycleData } from "../hooks/useCycleData";
import { DateUtils } from "../utils/format";
import { THEME_COLORS } from "../types/constants";
import { SoftCard } from "../components/SoftCard";
import { SoftButton } from "../components/SoftButton";
import { useCallback } from "react";
import { ActionCard } from "../components/ActionCard";
import { DailyTipsService } from "../utils/dailyTips";

export const HomeScreen: React.FC = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const {
    prediction,
    stats,
    analytics,
    entries,
    getCurrentCycleDay,
    getDaysUntilNextPeriod,
    getEntryForDate,
    loading,
    refreshData,
  } = useCycleData();

  const today = DateUtils.getTodayString();
  const todayEntry = getEntryForDate(today);
  const currentCycleDay = getCurrentCycleDay();
  const daysUntilPeriod = getDaysUntilNextPeriod();

  // Refresh data when screen comes into focus (e.g., returning from log entry)
  useFocusEffect(
    useCallback(() => {
      refreshData();
    }, [refreshData])
  );

  const handleLogToday = () => {
    router.push("/logEntry");
  };

  const handleViewCalendar = () => {
    router.push("/(tabs)/calendar");
  };

  const handleViewStats = () => {
    router.push("/(tabs)/stats");
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={{
        paddingTop: insets.top,
        paddingBottom: insets.bottom + 20, // Extra padding for content
      }}
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={refreshData} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.title}>LocalOne Period & Cycle</Text>
        <Text style={styles.subtitle}>Your personal cycle tracker</Text>
      </View>

      {/* Today's Status */}
      <SoftCard delay={100}>
        <Text style={styles.cardTitle}>
          Today - {DateUtils.formatForDisplay(today)}
        </Text>

        {todayEntry ? (
          <View style={styles.todayStatus}>
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Period Day:</Text>
              <Text
                style={[
                  styles.statusValue,
                  todayEntry.isPeriodDay && styles.periodText,
                ]}
              >
                {todayEntry.isPeriodDay ? "Yes" : "No"}
              </Text>
            </View>

            {todayEntry.isPeriodDay && todayEntry.flowLevel && (
              <View style={styles.statusRow}>
                <Text style={styles.statusLabel}>Flow:</Text>
                <Text style={styles.statusValue}>{todayEntry.flowLevel}</Text>
              </View>
            )}

            {todayEntry.mood && (
              <View style={styles.statusRow}>
                <Text style={styles.statusLabel}>Mood:</Text>
                <Text style={styles.statusValue}>{todayEntry.mood}</Text>
              </View>
            )}

            {todayEntry.symptoms.length > 0 && (
              <View style={styles.statusRow}>
                <Text style={styles.statusLabel}>Symptoms:</Text>
                <Text style={styles.statusValue}>
                  {todayEntry.symptoms.length} logged
                </Text>
              </View>
            )}

            <SoftButton
              title="Edit Today's Entry"
              onPress={handleLogToday}
              variant="primary"
              size="medium"
            />
          </View>
        ) : (
          <View style={styles.noDataContainer}>
            <Text style={styles.noDataText}>No data logged for today</Text>
            <SoftButton
              title="Log Today's Data"
              onPress={handleLogToday}
              variant="accent"
              size="large"
            />
          </View>
        )}
      </SoftCard>

      {/* Cycle Overview */}
      {stats && (
        <SoftCard delay={200}>
          <Text style={styles.cardTitle}>Cycle Overview</Text>

          <View style={styles.overviewGrid}>
            <View style={styles.overviewItem}>
              <Text style={styles.overviewValue}>
                {currentCycleDay !== null ? currentCycleDay : "—"}
              </Text>
              <Text style={styles.overviewLabel}>Cycle Day</Text>
            </View>

            <View style={styles.overviewItem}>
              <Text style={styles.overviewValue}>
                {stats.averageCycleLength}
              </Text>
              <Text style={styles.overviewLabel}>Cycle Length</Text>
            </View>

            <View style={styles.overviewItem}>
              <Text style={styles.overviewValue}>
                {stats.averagePeriodDuration}
              </Text>
              <Text style={styles.overviewLabel}>Period Length</Text>
            </View>
          </View>
        </SoftCard>
      )}

      {/* Learning Progress - Show when not enough data for predictions */}
      {!prediction && (
        <SoftCard delay={250}>
          <View style={styles.learningProgressContainer}>
            <Text style={styles.learningProgressIcon}>🧠</Text>
            <Text style={styles.learningProgressTitle}>
              Learning Your Patterns
            </Text>
            <Text style={styles.learningProgressText}>
              LocalOne Period & Cycle is building your personalized cycle
              profile. After tracking 2-3 complete cycles, you'll see accurate
              predictions here!
            </Text>

            <View style={styles.progressIndicator}>
              <Text style={styles.progressText}>
                Cycles tracked: {analytics?.cycleHistory.length || 0} / 3
              </Text>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${Math.min(
                        ((analytics?.cycleHistory.length || 0) / 3) * 100,
                        100
                      )}%`,
                    },
                  ]}
                />
              </View>
            </View>
          </View>
        </SoftCard>
      )}

      {/* Predictions */}
      {prediction && (
        <SoftCard delay={300}>
          <Text style={styles.cardTitle}>Predictions</Text>

          <View style={styles.predictionContainer}>
            <View style={styles.predictionItem}>
              <Text style={styles.predictionIcon}>🔴</Text>
              <View style={styles.predictionContent}>
                <Text style={styles.predictionTitle}>Next Period</Text>
                <Text style={styles.predictionDate}>
                  {DateUtils.formatForDisplay(prediction.nextPeriodDate)}
                </Text>
                {daysUntilPeriod !== null && (
                  <Text style={styles.predictionSubtext}>
                    {daysUntilPeriod > 0
                      ? `In ${daysUntilPeriod} days`
                      : daysUntilPeriod === 0
                      ? "Today"
                      : `${Math.abs(daysUntilPeriod)} days ago`}
                  </Text>
                )}
              </View>
            </View>

            <View style={styles.predictionItem}>
              <Text style={styles.predictionIcon}>🌱</Text>
              <View style={styles.predictionContent}>
                <Text style={styles.predictionTitle}>Fertile Window</Text>
                <Text style={styles.predictionDate}>
                  {DateUtils.formatForDisplay(prediction.fertileWindowStart)} -{" "}
                  {DateUtils.formatForDisplay(prediction.fertileWindowEnd)}
                </Text>
                <Text style={styles.predictionSubtext}>
                  Confidence: {prediction.confidence}
                </Text>
              </View>
            </View>
          </View>
        </SoftCard>
      )}

      {/* Quick Actions */}
      <SoftCard delay={400}>
        <Text style={styles.cardTitle}>Quick Actions</Text>

        <View style={styles.actionsGrid}>
          <ActionCard
            iconName="event"
            title="Calendar"
            subtitle="View your cycle"
            onPress={handleViewCalendar}
            delay={100}
            color={THEME_COLORS.primary}
          />

          <ActionCard
            iconName="analytics"
            title="Insights"
            subtitle="Track patterns"
            onPress={handleViewStats}
            delay={200}
            color={THEME_COLORS.accent}
          />

          <ActionCard
            iconName="add-circle"
            title="Log Today"
            subtitle="Add entry"
            onPress={handleLogToday}
            delay={300}
            color="#FFB347"
          />
        </View>
      </SoftCard>

      {/* Daily Tips */}
      <SoftCard delay={500}>
        {(() => {
          const dailyTip = DailyTipsService.getTipOfTheDay(
            entries,
            prediction,
            stats
          );
          return (
            <>
              <View style={styles.tipHeader}>
                <Text style={styles.cardTitle}>
                  {dailyTip.icon} {dailyTip.title}
                </Text>
                <TouchableOpacity
                  style={styles.viewAllTipsButton}
                  onPress={() => {
                    router.push("/tips");
                  }}
                >
                  <Text style={styles.viewAllTipsText}>More Tips</Text>
                </TouchableOpacity>
              </View>
              <Text style={styles.tipText}>{dailyTip.content}</Text>
            </>
          );
        })()}
      </SoftCard>

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME_COLORS.background,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 32,
    fontWeight: "300",
    color: THEME_COLORS.primary,
    textAlign: "center",
    letterSpacing: 1,
  },
  subtitle: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    textAlign: "center",
    marginTop: 8,
    fontWeight: "300",
  },
  card: {
    backgroundColor: THEME_COLORS.surface,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 20,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: "500",
    color: THEME_COLORS.text,
    marginBottom: 20,
    letterSpacing: 0.5,
  },
  todayStatus: {
    gap: 12,
  },
  statusRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  statusLabel: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    fontWeight: "500",
  },
  statusValue: {
    fontSize: 16,
    color: THEME_COLORS.text,
    fontWeight: "600",
  },
  periodText: {
    color: THEME_COLORS.period,
  },
  editTodayButton: {
    backgroundColor: THEME_COLORS.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 8,
  },
  editTodayButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  noDataContainer: {
    alignItems: "center",
    paddingVertical: 20,
  },
  noDataText: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    marginBottom: 16,
    textAlign: "center",
  },
  logTodayButton: {
    backgroundColor: THEME_COLORS.accent,
    paddingVertical: 14,
    paddingHorizontal: 32,
    borderRadius: 8,
    alignItems: "center",
  },
  logTodayButtonText: {
    color: "white",
    fontSize: 18,
    fontWeight: "600",
  },
  overviewGrid: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  overviewItem: {
    flex: 1,
    alignItems: "center",
  },
  overviewValue: {
    fontSize: 24,
    fontWeight: "bold",
    color: THEME_COLORS.primary,
  },
  overviewLabel: {
    fontSize: 12,
    color: THEME_COLORS.textSecondary,
    textAlign: "center",
    marginTop: 4,
    paddingHorizontal: 4,
  },
  predictionContainer: {
    gap: 16,
  },
  predictionItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  predictionIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  predictionContent: {
    flex: 1,
  },
  predictionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: THEME_COLORS.text,
  },
  predictionDate: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    marginTop: 2,
  },
  predictionSubtext: {
    fontSize: 12,
    color: THEME_COLORS.textSecondary,
    marginTop: 2,
  },
  actionsGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 12,
  },
  tipHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    lineHeight: 22,
    fontWeight: "300",
    textAlign: "center",
  },
  viewAllTipsButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    backgroundColor: THEME_COLORS.primary + "20",
  },
  viewAllTipsText: {
    fontSize: 12,
    color: THEME_COLORS.primary,
    fontWeight: "600",
  },
  bottomSpacing: {
    height: 40,
  },
  // Learning Progress Styles
  learningProgressContainer: {
    alignItems: "center",
    paddingVertical: 8,
  },
  learningProgressIcon: {
    fontSize: 32,
    marginBottom: 12,
  },
  learningProgressTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: THEME_COLORS.text,
    marginBottom: 8,
    textAlign: "center",
  },
  learningProgressText: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    textAlign: "center",
    lineHeight: 20,
    marginBottom: 16,
    paddingHorizontal: 8,
  },
  progressIndicator: {
    width: "100%",
    alignItems: "center",
  },
  progressText: {
    fontSize: 14,
    color: THEME_COLORS.text,
    fontWeight: "500",
    marginBottom: 8,
  },
  progressBar: {
    width: "80%",
    height: 6,
    backgroundColor: THEME_COLORS.border,
    borderRadius: 3,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: THEME_COLORS.primary,
    borderRadius: 3,
  },
});
