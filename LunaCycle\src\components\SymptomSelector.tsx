import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from "react-native";
import { SymptomType } from "../types/CycleEntry";
import { SYMPTOM_OPTIONS, THEME_COLORS } from "../types/constants";

interface SymptomSelectorProps {
  selectedSymptoms: SymptomType[];
  onSymptomsChange: (symptoms: SymptomType[]) => void;
  title?: string;
  style?: any;
}

export const SymptomSelector: React.FC<SymptomSelectorProps> = ({
  selectedSymptoms,
  onSymptomsChange,
  title = "Any symptoms?",
  style,
}) => {
  const toggleSymptom = (symptom: SymptomType) => {
    const isSelected = selectedSymptoms.includes(symptom);
    if (isSelected) {
      onSymptomsChange(selectedSymptoms.filter((s) => s !== symptom));
    } else {
      onSymptomsChange([...selectedSymptoms, symptom]);
    }
  };

  // Group symptoms by category
  const groupedSymptoms = SYMPTOM_OPTIONS.reduce((groups, symptom) => {
    const category = symptom.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(symptom);
    return groups;
  }, {} as Record<string, typeof SYMPTOM_OPTIONS>);

  const categoryTitles = {
    physical: "Physical",
    digestive: "Digestive",
    emotional: "Emotional",
    other: "Other",
  };

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.title}>{title}</Text>
      <View style={styles.scrollContainer}>
        {Object.entries(groupedSymptoms).map(([category, symptoms]) => (
          <View key={category} style={styles.categoryContainer}>
            <Text style={styles.categoryTitle}>
              {categoryTitles[category as keyof typeof categoryTitles]}
            </Text>
            <View style={styles.symptomsGrid}>
              {symptoms.map((symptom) => {
                const isSelected = selectedSymptoms.includes(symptom.value);
                return (
                  <TouchableOpacity
                    key={symptom.value}
                    style={[
                      styles.symptomButton,
                      isSelected && styles.selectedSymptomButton,
                    ]}
                    onPress={() => toggleSymptom(symptom.value)}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.symptomIcon}>{symptom.icon}</Text>
                    <Text
                      style={[
                        styles.symptomLabel,
                        isSelected && styles.selectedSymptomLabel,
                      ]}
                    >
                      {symptom.label}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        ))}
      </View>
      {selectedSymptoms.length > 0 && (
        <View style={styles.selectedContainer}>
          <Text style={styles.selectedTitle}>
            Selected ({selectedSymptoms.length}):
          </Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.selectedScrollView}
          >
            {selectedSymptoms.map((symptom) => {
              const symptomOption = SYMPTOM_OPTIONS.find(
                (s) => s.value === symptom
              );
              return (
                <TouchableOpacity
                  key={symptom}
                  style={styles.selectedSymptomChip}
                  onPress={() => toggleSymptom(symptom)}
                >
                  <Text style={styles.selectedSymptomText}>
                    {symptomOption?.icon} {symptomOption?.label}
                  </Text>
                  <Text style={styles.removeSymptomText}>×</Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: THEME_COLORS.text,
    marginBottom: 12,
  },
  scrollContainer: {
    // Remove maxHeight to allow full content display
  },
  categoryContainer: {
    marginBottom: 20,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: THEME_COLORS.primary,
    marginBottom: 8,
  },
  symptomsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  symptomButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: THEME_COLORS.border,
    backgroundColor: THEME_COLORS.surface,
    marginBottom: 8,
  },
  selectedSymptomButton: {
    backgroundColor: THEME_COLORS.primary + "20",
    borderColor: THEME_COLORS.primary,
  },
  symptomIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  symptomLabel: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    fontWeight: "500",
  },
  selectedSymptomLabel: {
    color: THEME_COLORS.text,
    fontWeight: "600",
  },
  selectedContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: THEME_COLORS.border,
  },
  selectedTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: THEME_COLORS.text,
    marginBottom: 8,
  },
  selectedScrollView: {
    flexGrow: 0,
  },
  selectedSymptomChip: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: THEME_COLORS.primary,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginRight: 8,
  },
  selectedSymptomText: {
    color: "white",
    fontSize: 12,
    fontWeight: "500",
    marginRight: 6,
  },
  removeSymptomText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
});
