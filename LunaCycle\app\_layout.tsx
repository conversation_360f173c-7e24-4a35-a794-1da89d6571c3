import {
  DarkTheme,
  <PERSON><PERSON><PERSON><PERSON>hem<PERSON>,
  Theme<PERSON>rovider,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { SafeAreaProvider } from "react-native-safe-area-context";
import "react-native-reanimated";

import { useColorScheme } from "@/hooks/useColorScheme";
import { ToastWrapper } from "@/src/components/ToastWrapper";
import { NotificationService } from "@/src/utils/notificationService";
import { useEffect } from "react";

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  useEffect(() => {
    // Initialize notification service when app starts
    NotificationService.initialize();
  }, []);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <SafeAreaProvider>
      <ThemeProvider value={colorScheme === "dark" ? DarkTheme : DefaultTheme}>
        <Stack>
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen
            name="about"
            options={{
              title: "About",
              headerShown: true,
              headerBackTitle: "Back",
              headerStyle: {
                backgroundColor: "#B19CD9",
              },
              headerTintColor: "#fff",
              headerTitleStyle: {
                fontWeight: "bold",
              },
            }}
          />
          <Stack.Screen
            name="dataHistory"
            options={{
              title: "Data History",
              headerShown: true,
              headerBackTitle: "Back",
              headerStyle: {
                backgroundColor: "#B19CD9",
              },
              headerTintColor: "#fff",
              headerTitleStyle: {
                fontWeight: "bold",
              },
            }}
          />
          <Stack.Screen
            name="logEntry"
            options={{
              presentation: "modal",
              headerShown: false,
            }}
          />
          <Stack.Screen name="+not-found" />
        </Stack>
        <StatusBar style="auto" />
        <ToastWrapper />
      </ThemeProvider>
    </SafeAreaProvider>
  );
}
