import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StatusBar,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { useCycleData } from "../hooks/useCycleData";
import { MoodSelector } from "../components/MoodSelector";
import { SymptomSelector } from "../components/SymptomSelector";
import { FlowPicker } from "../components/FlowPicker";
import { DateUtils } from "../utils/format";
import { THEME_COLORS } from "../types/constants";
import { FlowLevel, MoodType, SymptomType } from "../types/CycleEntry";
import { showToast } from "../components/ToastWrapper";

export const LogEntryScreen: React.FC = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const date =
    (params.date as string) || new Date().toISOString().split("T")[0];
  const existingEntry = params.existingEntry as string;
  const { addOrUpdateEntry, getEntryForDate, deleteEntry } = useCycleData();

  // Form state
  const [isPeriodDay, setIsPeriodDay] = useState(false);
  const [flowLevel, setFlowLevel] = useState<FlowLevel | undefined>(undefined);
  const [mood, setMood] = useState<MoodType | undefined>(undefined);
  const [symptoms, setSymptoms] = useState<SymptomType[]>([]);
  const [notes, setNotes] = useState("");
  const [loading, setSaving] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState(false);

  // Load existing entry data
  useEffect(() => {
    const entry = getEntryForDate(date);
    if (entry) {
      setIsPeriodDay(entry.isPeriodDay);
      setFlowLevel(entry.flowLevel);
      setMood(entry.mood);
      setSymptoms(entry.symptoms);
      setNotes(entry.notes || "");
    }
  }, [date, getEntryForDate]);

  const handleSave = async () => {
    try {
      setSaving(true);

      await addOrUpdateEntry({
        date,
        isPeriodDay,
        flowLevel: isPeriodDay ? flowLevel : undefined,
        mood,
        symptoms,
        notes: notes.trim() || undefined,
      });

      router.back();
    } catch (error) {
      console.error("Error saving entry:", error);
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = () => {
    const entry = getEntryForDate(date);
    if (!entry) return;

    if (!deleteConfirmation) {
      // First tap - show warning toast and set confirmation state
      setDeleteConfirmation(true);
      showToast.deleteConfirm(
        "Delete Entry",
        "Tap delete again within 8 seconds to confirm"
      );

      // Reset confirmation after 8 seconds
      setTimeout(() => {
        setDeleteConfirmation(false);
      }, 8000);
    } else {
      // Second tap - proceed with deletion
      setDeleteConfirmation(false); // Reset state

      // The deleteEntry function in useCycleData already shows a success toast
      deleteEntry(entry.id)
        .then(() => {
          router.back();
        })
        .catch((error) => {
          console.error("Error deleting entry:", error);
          showToast.error(
            "Delete Failed",
            "Could not delete entry. Try again."
          );
        });
    }
  };

  const isFormValid =
    isPeriodDay || mood || symptoms.length > 0 || notes.trim();
  const existingEntryData = getEntryForDate(date);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={THEME_COLORS.surface}
      />
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Text style={styles.cancelButton}>Cancel</Text>
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <Text style={styles.headerTitle}>Log Entry</Text>
            <Text style={styles.headerDate}>
              {DateUtils.formatWithDayName(date)}
            </Text>
          </View>
          <TouchableOpacity
            onPress={handleSave}
            disabled={!isFormValid || loading}
            style={[
              styles.saveButton,
              (!isFormValid || loading) && styles.saveButtonDisabled,
            ]}
          >
            <Text
              style={[
                styles.saveButtonText,
                (!isFormValid || loading) && styles.saveButtonTextDisabled,
              ]}
            >
              {loading ? "Saving..." : "Save"}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          {/* Period Day Toggle */}
          <View style={styles.section}>
            <View style={styles.periodToggleContainer}>
              <View>
                <Text style={styles.sectionTitle}>Period Day</Text>
                <Text style={styles.sectionSubtitle}>
                  Was this a period day?
                </Text>
              </View>
              <Switch
                value={isPeriodDay}
                onValueChange={setIsPeriodDay}
                trackColor={{
                  false: THEME_COLORS.border,
                  true: THEME_COLORS.primary + "40",
                }}
                thumbColor={
                  isPeriodDay
                    ? THEME_COLORS.primary
                    : THEME_COLORS.textSecondary
                }
              />
            </View>
          </View>

          {/* Flow Level (only if period day) */}
          {isPeriodDay && (
            <View style={styles.section}>
              <FlowPicker
                selectedFlow={flowLevel}
                onFlowSelect={setFlowLevel}
                title="Flow Level"
              />
            </View>
          )}

          {/* Mood Selector */}
          <View style={styles.section}>
            <MoodSelector
              selectedMood={mood}
              onMoodSelect={setMood}
              title="Mood"
            />
          </View>

          {/* Symptom Selector */}
          <View style={styles.section}>
            <SymptomSelector
              selectedSymptoms={symptoms}
              onSymptomsChange={setSymptoms}
              title="Symptoms"
            />
          </View>

          {/* Notes */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notes</Text>
            <TextInput
              style={styles.notesInput}
              value={notes}
              onChangeText={setNotes}
              placeholder="Add any additional notes..."
              placeholderTextColor={THEME_COLORS.textSecondary}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          {/* Delete Button (only if entry exists) */}
          {existingEntryData && (
            <View style={styles.section}>
              <TouchableOpacity
                style={[
                  styles.deleteButton,
                  deleteConfirmation && styles.deleteButtonConfirm,
                ]}
                onPress={handleDelete}
              >
                <Text style={styles.deleteButtonText}>
                  {deleteConfirmation
                    ? "⚠️ Tap Again to Delete"
                    : "Delete Entry"}
                </Text>
              </TouchableOpacity>
            </View>
          )}

          <View style={styles.bottomSpacing} />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME_COLORS.background,
  },
  keyboardContainer: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingTop: Platform.OS === "ios" ? 20 : 40,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: THEME_COLORS.border,
    backgroundColor: THEME_COLORS.surface,
  },
  cancelButton: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    fontWeight: "500",
  },
  headerCenter: {
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: THEME_COLORS.text,
  },
  headerDate: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    marginTop: 2,
  },
  saveButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: THEME_COLORS.primary,
  },
  saveButtonDisabled: {
    backgroundColor: THEME_COLORS.border,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "white",
  },
  saveButtonTextDisabled: {
    color: THEME_COLORS.textSecondary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: THEME_COLORS.text,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
  },
  periodToggleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: THEME_COLORS.border,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: THEME_COLORS.text,
    backgroundColor: THEME_COLORS.surface,
    minHeight: 100,
    maxHeight: 150,
  },
  deleteButton: {
    backgroundColor: THEME_COLORS.error,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 16,
  },
  deleteButtonConfirm: {
    backgroundColor: "#FF4444",
    borderWidth: 2,
    borderColor: "#FF0000",
    shadowColor: "#FF0000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  deleteButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  scrollContent: {
    paddingTop: 20,
    paddingBottom: 40,
    flexGrow: 1,
  },
  bottomSpacing: {
    height: 200,
  },
});
