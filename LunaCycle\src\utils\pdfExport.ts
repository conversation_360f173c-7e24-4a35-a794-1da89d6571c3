import * as Print from "expo-print";
import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
import { CycleEntry, CycleStats } from "../types/CycleEntry";
import { DateUtils, TextUtils } from "./format";
import { MOOD_OPTIONS, SYMPTOM_OPTIONS } from "../types/constants";

export class PDFExportService {
  // Helper function to list exported PDF files
  static async listExportedFiles(): Promise<string[]> {
    try {
      const files: string[] = [];

      // Check document directory
      try {
        const docFiles = await FileSystem.readDirectoryAsync(
          FileSystem.documentDirectory!
        );
        const pdfFiles = docFiles.filter(
          (file) =>
            file.startsWith("LunaCycle_Export_") && file.endsWith(".pdf")
        );
        files.push(...pdfFiles.map((file) => `Documents: ${file}`));
      } catch (error) {
        console.log("Could not read document directory:", error);
      }

      // Check cache directory
      try {
        const cacheFiles = await FileSystem.readDirectoryAsync(
          FileSystem.cacheDirectory!
        );
        const pdfFiles = cacheFiles.filter(
          (file) =>
            file.startsWith("LunaCycle_Export_") && file.endsWith(".pdf")
        );
        files.push(...pdfFiles.map((file) => `Cache: ${file}`));
      } catch (error) {
        console.log("Could not read cache directory:", error);
      }

      return files;
    } catch (error) {
      console.error("Error listing exported files:", error);
      return [];
    }
  }
  static async exportCycleDataToPDF(
    entries: CycleEntry[],
    stats: CycleStats | null
  ): Promise<
    { success: true; filePath: string } | { success: false; error: string }
  > {
    try {
      const htmlContent = this.generateHTMLContent(entries, stats);

      // Generate PDF using Expo Print
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        width: 612,
        height: 792,
        margins: {
          left: 40,
          top: 40,
          right: 40,
          bottom: 40,
        },
      });

      // Generate filename with current date
      const fileName = `LunaCycle_Export_${
        new Date().toISOString().split("T")[0]
      }.pdf`;

      console.log("PDF generated successfully:", uri);

      // Check if sharing is available
      const isAvailable = await Sharing.isAvailableAsync();
      if (isAvailable) {
        // Use sharing to let user save the PDF where they want
        await Sharing.shareAsync(uri, {
          mimeType: "application/pdf",
          dialogTitle: `Save ${fileName}`,
        });

        console.log("PDF shared successfully");
        return { success: true, filePath: fileName };
      } else {
        // Fallback: save to document directory if sharing is not available
        const localUri = `${FileSystem.documentDirectory}${fileName}`;

        await FileSystem.copyAsync({
          from: uri,
          to: localUri,
        });

        const fileInfo = await FileSystem.getInfoAsync(localUri);
        if (fileInfo.exists) {
          console.log("PDF saved to document directory:", localUri);
          return { success: true, filePath: localUri };
        } else {
          throw new Error("Failed to save PDF to device storage");
        }
      }
    } catch (error) {
      console.error("PDF Export Error:", error);

      // Try a simpler HTML version if the complex one fails
      try {
        const simpleHtml = this.generateSimpleHTMLContent(entries, stats);
        const { uri } = await Print.printToFileAsync({
          html: simpleHtml,
          width: 612,
          height: 792,
        });

        const fileName = `LunaCycle_Export_${
          new Date().toISOString().split("T")[0]
        }.pdf`;

        // Try sharing the simple version
        const isAvailable = await Sharing.isAvailableAsync();
        if (isAvailable) {
          await Sharing.shareAsync(uri, {
            mimeType: "application/pdf",
            dialogTitle: `Save ${fileName}`,
          });
          return { success: true, filePath: fileName };
        } else {
          // Fallback to document directory
          const localUri = `${FileSystem.documentDirectory}${fileName}`;
          await FileSystem.copyAsync({ from: uri, to: localUri });

          const fileInfo = await FileSystem.getInfoAsync(localUri);
          if (fileInfo.exists) {
            return { success: true, filePath: localUri };
          }
        }
      } catch (fallbackError) {
        console.error("Fallback PDF Export Error:", fallbackError);
      }

      // Provide more specific error messages
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (errorMessage.includes("permission")) {
        return {
          success: false,
          error:
            "Permission denied. Please allow file access in your device settings.",
        };
      } else if (errorMessage.includes("storage")) {
        return {
          success: false,
          error: "Not enough storage space available for PDF export.",
        };
      } else {
        return {
          success: false,
          error: "Failed to generate PDF export. Please try again.",
        };
      }
    }
  }

  private static generateHTMLContent(
    entries: CycleEntry[],
    stats: CycleStats | null
  ): string {
    const today = new Date().toLocaleDateString();
    const totalEntries = entries.length;
    const periodDays = entries.filter((entry) => entry.isPeriodDay).length;

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>LunaCycle Data Export</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          text-align: center;
          border-bottom: 3px solid #B19CD9;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .header h1 {
          color: #B19CD9;
          font-size: 2.5em;
          margin: 0;
          font-weight: 300;
        }
        .header .subtitle {
          color: #8B8B8B;
          font-size: 1.1em;
          margin-top: 10px;
        }
        .stats-grid {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-around;
          gap: 20px;
          margin-bottom: 30px;
        }
        .stat-card {
          background: #F9F7FF;
          border: 1px solid #E8E4F0;
          border-radius: 12px;
          padding: 20px;
          text-align: center;
        }
        .stat-value {
          font-size: 2em;
          font-weight: bold;
          color: #B19CD9;
          margin-bottom: 5px;
        }
        .stat-label {
          color: #8B8B8B;
          font-size: 0.9em;
        }
        .section {
          margin-bottom: 40px;
        }
        .section h2 {
          color: #4A4A4A;
          border-bottom: 2px solid #E8E4F0;
          padding-bottom: 10px;
          margin-bottom: 20px;
        }
        .entry {
          background: #FEFEFE;
          border: 1px solid #E8E4F0;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
        }
        .entry-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }
        .entry-date {
          font-weight: 600;
          color: #4A4A4A;
          font-size: 1.1em;
        }
        .period-indicator {
          background: #FF9AA2;
          color: white;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 0.8em;
          font-weight: 600;
        }
        .entry-details {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
        }
        .detail-item {
          background: #F9F7FF;
          padding: 10px;
          border-radius: 6px;
          min-width: 150px;
          flex: 1;
        }
        .detail-label {
          font-weight: 600;
          color: #8B8B8B;
          font-size: 0.8em;
          text-transform: uppercase;
          margin-bottom: 5px;
        }
        .detail-value {
          color: #4A4A4A;
        }
        .symptoms-list {
          display: flex;
          flex-wrap: wrap;
          gap: 5px;
        }
        .symptom-tag {
          background: #E6D7FF;
          color: #4A4A4A;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 0.8em;
        }
        .footer {
          text-align: center;
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #E8E4F0;
          color: #8B8B8B;
          font-size: 0.9em;
        }
        .no-data {
          text-align: center;
          color: #8B8B8B;
          font-style: italic;
          padding: 40px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🌙 LunaCycle</h1>
        <div class="subtitle">Personal Cycle Data Export</div>
        <div class="subtitle">Generated on ${today}</div>
      </div>

      ${this.generateStatsSection(stats, totalEntries, periodDays)}
      ${this.generateEntriesSection(entries)}

      <div class="footer">
        <p>This report was generated by LunaCycle - Your privacy-first period tracker</p>
        <p>All data is stored locally on your device</p>
      </div>
    </body>
    </html>
    `;
  }

  private static generateStatsSection(
    stats: CycleStats | null,
    totalEntries: number,
    periodDays: number
  ): string {
    if (!stats) {
      return `
        <div class="section">
          <h2>📊 Overview</h2>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-value">${totalEntries}</div>
              <div class="stat-label">Total Entries</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">${periodDays}</div>
              <div class="stat-label">Period Days Logged</div>
            </div>
          </div>
        </div>
      `;
    }

    return `
      <div class="section">
        <h2>📊 Cycle Statistics</h2>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value">${stats.averageCycleLength} days</div>
            <div class="stat-label">Avg Cycle Length</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">${stats.averagePeriodDuration} days</div>
            <div class="stat-label">Avg Period Duration</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">${totalEntries}</div>
            <div class="stat-label">Total Entries</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">${periodDays}</div>
            <div class="stat-label">Period Days Logged</div>
          </div>
        </div>
      </div>
    `;
  }

  private static generateEntriesSection(entries: CycleEntry[]): string {
    if (entries.length === 0) {
      return `
        <div class="section">
          <h2>📅 Cycle Entries</h2>
          <div class="no-data">No cycle data available</div>
        </div>
      `;
    }

    const sortedEntries = [...entries].sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    const entriesHTML = sortedEntries
      .map((entry) => {
        const moodText = entry.mood
          ? MOOD_OPTIONS.find((m) => m.value === entry.mood)?.label ||
            entry.mood
          : "Not recorded";

        const symptomsText =
          entry.symptoms.length > 0
            ? entry.symptoms
                .map(
                  (symptom) =>
                    SYMPTOM_OPTIONS.find((s) => s.value === symptom)?.label ||
                    symptom
                )
                .join(", ")
            : "None recorded";

        return `
        <div class="entry">
          <div class="entry-header">
            <div class="entry-date">${DateUtils.formatForDisplay(
              entry.date
            )}</div>
            ${
              entry.isPeriodDay
                ? '<div class="period-indicator">Period Day</div>'
                : ""
            }
          </div>
          <div class="entry-details">
            <div class="detail-item">
              <div class="detail-label">Mood</div>
              <div class="detail-value">${moodText}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Symptoms</div>
              <div class="detail-value">${symptomsText}</div>
            </div>
            ${
              entry.notes
                ? `
              <div class="detail-item" style="grid-column: 1 / -1;">
                <div class="detail-label">Notes</div>
                <div class="detail-value">${entry.notes}</div>
              </div>
            `
                : ""
            }
          </div>
        </div>
      `;
      })
      .join("");

    return `
      <div class="section">
        <h2>📅 Cycle Entries (${entries.length} total)</h2>
        ${entriesHTML}
      </div>
    `;
  }

  private static generateSimpleHTMLContent(
    entries: CycleEntry[],
    stats: CycleStats | null
  ): string {
    const today = new Date().toLocaleDateString();
    const totalEntries = entries.length;
    const periodDays = entries.filter((entry) => entry.isPeriodDay).length;

    const entriesHTML = entries
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 20) // Limit to 20 most recent entries
      .map((entry) => {
        const moodText = entry.mood
          ? MOOD_OPTIONS.find((m) => m.value === entry.mood)?.label ||
            entry.mood
          : "Not recorded";

        const symptomsText =
          entry.symptoms.length > 0
            ? entry.symptoms
                .map(
                  (symptom) =>
                    SYMPTOM_OPTIONS.find((s) => s.value === symptom)?.label ||
                    symptom
                )
                .join(", ")
            : "None";

        return `
          <div style="margin-bottom: 15px; padding: 10px; border: 1px solid #E8E4F0; border-radius: 8px;">
            <h4 style="margin: 0 0 8px 0; color: #4A4A4A;">
              ${DateUtils.formatForDisplay(entry.date)}
              ${entry.isPeriodDay ? " - Period Day" : ""}
            </h4>
            <p style="margin: 4px 0;"><strong>Mood:</strong> ${moodText}</p>
            <p style="margin: 4px 0;"><strong>Symptoms:</strong> ${symptomsText}</p>
            ${
              entry.notes
                ? `<p style="margin: 4px 0;"><strong>Notes:</strong> ${entry.notes}</p>`
                : ""
            }
          </div>
        `;
      })
      .join("");

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>LunaCycle Data Export</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.4; }
          h1 { color: #B19CD9; text-align: center; }
          h2 { color: #4A4A4A; border-bottom: 2px solid #E8E4F0; padding-bottom: 8px; }
          .stats { display: flex; justify-content: space-around; margin: 20px 0; }
          .stat { text-align: center; padding: 10px; }
          .stat-value { font-size: 24px; font-weight: bold; color: #B19CD9; }
          .stat-label { font-size: 12px; color: #8B8B8B; }
        </style>
      </head>
      <body>
        <h1>🌙 LunaCycle Data Export</h1>
        <p style="text-align: center; color: #8B8B8B;">Generated on ${today}</p>

        <h2>📊 Overview</h2>
        <div class="stats">
          <div class="stat">
            <div class="stat-value">${totalEntries}</div>
            <div class="stat-label">Total Entries</div>
          </div>
          <div class="stat">
            <div class="stat-value">${periodDays}</div>
            <div class="stat-label">Period Days</div>
          </div>
          ${
            stats
              ? `
            <div class="stat">
              <div class="stat-value">${stats.averageCycleLength}</div>
              <div class="stat-label">Avg Cycle Length</div>
            </div>
            <div class="stat">
              <div class="stat-value">${stats.averagePeriodDuration}</div>
              <div class="stat-label">Avg Period Duration</div>
            </div>
          `
              : ""
          }
        </div>

        <h2>📅 Recent Entries</h2>
        ${
          entriesHTML ||
          '<p style="text-align: center; color: #8B8B8B;">No entries available</p>'
        }

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #E8E4F0; color: #8B8B8B; font-size: 12px;">
          <p>Generated by LunaCycle - Your privacy-first period tracker</p>
        </div>
      </body>
      </html>
    `;
  }
}
