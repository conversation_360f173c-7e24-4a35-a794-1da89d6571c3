import { useState, useEffect, useCallback } from "react";
import {
  CycleEntry,
  CyclePrediction,
  CycleStats,
  CycleAnalytics,
} from "../types/CycleEntry";
import { CycleStorage } from "../utils/storage";
import { CyclePredictionEngine } from "../utils/predictCycle";
import { DateUtils } from "../utils/format";
import { showToast } from "../components/ToastWrapper";

export const useCycleData = () => {
  const [entries, setEntries] = useState<CycleEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [prediction, setPrediction] = useState<CyclePrediction | null>(null);
  const [stats, setStats] = useState<CycleStats | null>(null);
  const [analytics, setAnalytics] = useState<CycleAnalytics | null>(null);
  const [refreshKey, setRefreshKey] = useState(0); // Force refresh trigger

  // Load initial data
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      const loadedEntries = await CycleStorage.loadCycleEntries();
      setEntries(loadedEntries);

      // Calculate predictions and stats with error handling
      try {
        const cycles = CyclePredictionEngine.extractCycles(loadedEntries);
        console.log("Extracted cycles:", cycles.length, cycles);

        const newPrediction = CyclePredictionEngine.generatePrediction(cycles);
        console.log("Generated prediction:", newPrediction);

        const newStats = CyclePredictionEngine.calculateStats(cycles);
        console.log("Calculated stats:", newStats);

        const newAnalytics =
          CyclePredictionEngine.generateAnalytics(loadedEntries);
        console.log("Generated analytics:", newAnalytics);

        setPrediction(newPrediction);
        setStats(newStats);
        setAnalytics(newAnalytics);
      } catch (predictionError) {
        console.error("Error in prediction calculations:", predictionError);
        // Set safe fallback values
        setPrediction(null);
        setStats(null);
        setAnalytics(null);
        showToast.error(
          "Prediction Error",
          "Unable to calculate cycle predictions"
        );
      }
    } catch (error) {
      console.error("Error loading cycle data:", error);
      showToast.error("Error", "Failed to load cycle data");
    } finally {
      setLoading(false);
    }
  }, []);

  // Add or update cycle entry
  const addOrUpdateEntry = useCallback(
    async (entryData: Omit<CycleEntry, "id" | "createdAt" | "updatedAt">) => {
      try {
        setLoading(true);

        const existingEntry = await CycleStorage.getCycleEntryByDate(
          entryData.date
        );

        const entry: CycleEntry = {
          id:
            existingEntry?.id ||
            `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          ...entryData,
          createdAt: existingEntry?.createdAt || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        // Save to storage
        await CycleStorage.addCycleEntry(entry);

        // Force immediate refresh trigger to ensure all components re-render
        setRefreshKey((prev) => prev + 1);

        // Small delay to ensure state updates are processed
        await new Promise((resolve) => setTimeout(resolve, 5));

        // Reload all data to get updated predictions and stats
        await loadData();

        showToast.success(
          existingEntry ? "Entry Updated" : "Entry Added",
          `Data for ${DateUtils.formatForDisplay(
            entryData.date
          )} has been saved`
        );
      } catch (error) {
        console.error("Error saving entry:", error);
        showToast.error("Error", "Failed to save entry");
      } finally {
        setLoading(false);
      }
    },
    [loadData]
  );

  // Delete cycle entry
  const deleteEntry = useCallback(
    async (entryId: string) => {
      try {
        setLoading(true);

        // Delete from storage
        await CycleStorage.deleteCycleEntry(entryId);

        // Force immediate refresh trigger to ensure all components re-render
        setRefreshKey((prev) => prev + 1);

        // Small delay to ensure state updates are processed
        await new Promise((resolve) => setTimeout(resolve, 5));

        // Reload all data to get updated predictions and stats
        await loadData();

        showToast.success(
          "Entry Deleted",
          "Entry has been removed successfully"
        );
      } catch (error) {
        console.error("Error deleting entry:", error);
        showToast.error("Error", "Failed to delete entry");
      } finally {
        setLoading(false);
      }
    },
    [loadData]
  );

  // Get entry for specific date
  const getEntryForDate = useCallback(
    (date: string): CycleEntry | undefined => {
      return entries.find((entry) => entry.date === date);
    },
    [entries]
  );

  // Get entries for date range
  const getEntriesForRange = useCallback(
    (startDate: string, endDate: string): CycleEntry[] => {
      return entries.filter((entry) => {
        return entry.date >= startDate && entry.date <= endDate;
      });
    },
    [entries]
  );

  // Check if date has period
  const isPeriodDay = useCallback(
    (date: string): boolean => {
      const entry = getEntryForDate(date);
      return entry?.isPeriodDay || false;
    },
    [getEntryForDate]
  );

  // Check if date is in fertile window
  const isInFertileWindow = useCallback(
    (date: string): boolean => {
      if (!prediction) return false;
      return CyclePredictionEngine.isInFertileWindow(date, prediction);
    },
    [prediction]
  );

  // Check if date is predicted period day
  const isPredictedPeriodDay = useCallback(
    (date: string): boolean => {
      if (!prediction || !stats) return false;
      return CyclePredictionEngine.isPredictedPeriodDay(
        date,
        prediction,
        stats.averagePeriodDuration
      );
    },
    [prediction, stats]
  );

  // Get current cycle day
  const getCurrentCycleDay = useCallback((): number | null => {
    const cycles = CyclePredictionEngine.extractCycles(entries);
    return CyclePredictionEngine.getCurrentCycleDay(cycles);
  }, [entries]);

  // Get days until next period
  const getDaysUntilNextPeriod = useCallback((): number | null => {
    if (!prediction) return null;
    return CyclePredictionEngine.getDaysUntilNextPeriod(prediction);
  }, [prediction]);

  // Export data
  const exportData = useCallback(async (): Promise<string> => {
    try {
      return await CycleStorage.exportCycleData();
    } catch (error) {
      console.error("Error exporting data:", error);
      showToast.error("Error", "Failed to export data");
      throw error;
    }
  }, []);

  // Import data
  const importData = useCallback(
    async (jsonData: string, mergeWithExisting: boolean = false) => {
      try {
        setLoading(true);

        // Import data to storage
        await CycleStorage.importCycleData(jsonData, mergeWithExisting);

        // Force immediate refresh trigger to ensure all components re-render
        setRefreshKey((prev) => prev + 1);

        // Small delay to ensure state updates are processed
        await new Promise((resolve) => setTimeout(resolve, 5));

        // Reload all data to get updated predictions and stats
        await loadData();

        showToast.success("Import Successful", "Data has been imported");
      } catch (error) {
        console.error("Error importing data:", error);
        showToast.error("Error", "Failed to import data");
        throw error;
      } finally {
        setLoading(false);
      }
    },
    [loadData]
  );

  // Clear all data
  const clearAllData = useCallback(async () => {
    try {
      setLoading(true);

      // Clear storage first
      await CycleStorage.clearAllCycleData();

      // Immediately reset state to ensure UI updates
      setEntries([]);
      setPrediction(null);
      setStats(null);
      setAnalytics(null);
      setRefreshKey((prev) => prev + 1);

      // Small delay to ensure DOM updates are processed
      await new Promise((resolve) => setTimeout(resolve, 5));

      // Reload data to ensure consistency (should be empty)
      await loadData();

      showToast.success("Data Cleared", "All cycle data removed successfully");
    } catch (error) {
      console.error("Error clearing data:", error);
      showToast.error("Error", "Failed to clear data");
    } finally {
      setLoading(false);
    }
  }, [loadData]);

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    // Data
    entries,
    prediction,
    stats,
    analytics,
    loading,

    // Actions
    addOrUpdateEntry,
    deleteEntry,
    exportData,
    importData,
    clearAllData,
    refreshData: loadData,

    // Queries
    getEntryForDate,
    getEntriesForRange,
    isPeriodDay,
    isInFertileWindow,
    isPredictedPeriodDay,
    getCurrentCycleDay,
    getDaysUntilNextPeriod,
  };
};
