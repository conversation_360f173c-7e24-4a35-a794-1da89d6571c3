import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { DailyTipsService, DailyTip } from "../utils/dailyTips";
import { THEME_COLORS } from "../types/constants";
import { SoftCard } from "../components/SoftCard";

const CATEGORIES = [
  { key: "general" as const, label: "General", icon: "💡" },
  { key: "tracking" as const, label: "Tracking", icon: "📝" },
  { key: "wellness" as const, label: "Wellness", icon: "🌿" },
  { key: "period" as const, label: "Period", icon: "🩸" },
  { key: "fertile" as const, label: "Fertile", icon: "🌸" },
  { key: "pms" as const, label: "PMS", icon: "🧘‍♀️" },
];

export const TipsScreen: React.FC = () => {
  const insets = useSafeAreaInsets();
  const [selectedCategory, setSelectedCategory] =
    useState<DailyTip["category"]>("general");

  const tips = DailyTipsService.getAllTipsByCategory(selectedCategory);

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={{
        paddingTop: insets.top,
        paddingBottom: insets.bottom + 20, // Extra padding for content
      }}
    >
      <View style={styles.header}>
        <Text style={styles.title}>Health Tips</Text>
        <Text style={styles.subtitle}>
          Helpful advice for your cycle journey
        </Text>
      </View>

      {/* Category Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoryContainer}
        contentContainerStyle={styles.categoryContent}
      >
        {CATEGORIES.map((category) => (
          <TouchableOpacity
            key={category.key}
            style={[
              styles.categoryTab,
              selectedCategory === category.key && styles.categoryTabActive,
            ]}
            onPress={() => setSelectedCategory(category.key)}
          >
            <Text style={styles.categoryIcon}>{category.icon}</Text>
            <Text
              style={[
                styles.categoryLabel,
                selectedCategory === category.key && styles.categoryLabelActive,
              ]}
            >
              {category.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Tips List */}
      <View style={styles.tipsContainer}>
        {tips.map((tip, index) => (
          <SoftCard key={tip.id} delay={index * 100}>
            <View style={styles.tipCard}>
              <View style={styles.tipHeader}>
                <Text style={styles.tipIcon}>{tip.icon}</Text>
                <Text style={styles.tipTitle}>{tip.title}</Text>
              </View>
              <Text style={styles.tipContent}>{tip.content}</Text>
            </View>
          </SoftCard>
        ))}
      </View>

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME_COLORS.background,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: THEME_COLORS.text,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    color: THEME_COLORS.textSecondary,
    textAlign: "center",
    marginTop: 4,
  },
  categoryContainer: {
    marginVertical: 20,
  },
  categoryContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  categoryTab: {
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: THEME_COLORS.surface,
    borderWidth: 1,
    borderColor: THEME_COLORS.border,
    minWidth: 80,
  },
  categoryTabActive: {
    backgroundColor: THEME_COLORS.primary,
    borderColor: THEME_COLORS.primary,
  },
  categoryIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  categoryLabel: {
    fontSize: 12,
    color: THEME_COLORS.textSecondary,
    fontWeight: "500",
  },
  categoryLabelActive: {
    color: "white",
    fontWeight: "600",
  },
  tipsContainer: {
    paddingHorizontal: 16,
    gap: 16,
  },
  tipCard: {
    padding: 4,
  },
  tipHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  tipIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  tipTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: THEME_COLORS.text,
    flex: 1,
  },
  tipContent: {
    fontSize: 14,
    color: THEME_COLORS.textSecondary,
    lineHeight: 20,
  },
  bottomSpacing: {
    height: 40,
  },
});
